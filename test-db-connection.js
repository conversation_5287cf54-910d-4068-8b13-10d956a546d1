/**
 * Simple test script to verify database connection
 * Run with: node test-db-connection.js
 */

const { Pool } = require('pg')
require('dotenv').config({ path: '.env.local' })

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
})

async function testConnection() {
  try {
    console.log('🔌 Testing database connection...')
    
    // Test basic connection
    const client = await pool.connect()
    console.log('✅ Database connected successfully!')
    
    // Test query
    const result = await client.query('SELECT NOW() as current_time')
    console.log('⏰ Current database time:', result.rows[0].current_time)
    
    // Check if tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `)
    
    console.log('📋 Tables in database:')
    tablesResult.rows.forEach(row => {
      console.log(`  - ${row.table_name}`)
    })
    
    // Check table row counts
    const tables = ['users', 'checklists', 'journal_entries', 'course_progress', 'user_preferences']
    
    console.log('\n📊 Table row counts:')
    for (const table of tables) {
      try {
        const countResult = await client.query(`SELECT COUNT(*) FROM ${table}`)
        console.log(`  - ${table}: ${countResult.rows[0].count} rows`)
      } catch (error) {
        console.log(`  - ${table}: Table not found`)
      }
    }
    
    client.release()
    console.log('\n🎉 Database test completed successfully!')
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message)
  } finally {
    await pool.end()
  }
}

testConnection()
