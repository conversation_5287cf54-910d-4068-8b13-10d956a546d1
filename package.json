{"name": "limitless-options-checklist", "version": "1.0.0", "description": "Professional trading checklist and journal for Limitless Options", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@types/pg": "^8.15.4", "date-fns": "^2.30.0", "dotenv": "^17.0.1", "framer-motion": "^10.16.16", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.294.0", "next": "14.0.4", "next-themes": "^0.2.1", "pg": "^8.16.3", "react": "^18.2.0", "react-calendar": "^4.6.0", "react-confetti": "^6.1.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-quill": "^2.0.0", "recharts": "^2.8.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.3"}}