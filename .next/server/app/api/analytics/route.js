"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/route";
exports.ids = ["app/api/analytics/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/route.js */ \"(rsc)/./src/app/api/analytics/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/route\",\n        pathname: \"/api/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/api/analytics/route.js\",\n    nextConfigOutput,\n    userland: _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/analytics/route.js":
/*!****************************************!*\
  !*** ./src/app/api/analytics/route.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/analytics */ \"(rsc)/./src/lib/analytics.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/**\n * API Route for real analytics data\n * Returns actual user statistics from database\n */ \n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const days = parseInt(searchParams.get(\"days\")) || 30;\n        const type = searchParams.get(\"type\") || \"summary\";\n        const userId = 1 // Default user ID\n        ;\n        if (type === \"trend\") {\n            const trendData = await (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_0__.getPerformanceTrend)(userId, days);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: true,\n                data: trendData\n            });\n        }\n        const analytics = await (0,_lib_analytics__WEBPACK_IMPORTED_MODULE_0__.getUserAnalytics)(userId, days);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            data: analytics\n        });\n    } catch (error) {\n        console.error(\"Analytics API error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/analytics.js":
/*!******************************!*\
  !*** ./src/lib/analytics.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getPerformanceTrend: () => (/* binding */ getPerformanceTrend),\n/* harmony export */   getUserAnalytics: () => (/* binding */ getUserAnalytics)\n/* harmony export */ });\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./src/lib/db.js\");\n/**\n * Real Analytics Service\n * Calculates actual user statistics from real data\n */ \n/**\n * Get real user statistics from database\n */ const getUserAnalytics = async (userId = 1, days = 30)=>{\n    try {\n        const endDate = new Date();\n        const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);\n        // Get checklist data\n        const checklistResult = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT \n        date,\n        items,\n        confidence_rating,\n        created_at\n      FROM checklists \n      WHERE user_id = $1 \n        AND date >= $2 \n        AND date <= $3\n      ORDER BY date DESC\n    `, [\n            userId,\n            startDate.toISOString().split(\"T\")[0],\n            endDate.toISOString().split(\"T\")[0]\n        ]);\n        // Get journal data\n        const journalResult = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT \n        date,\n        content,\n        tags,\n        created_at\n      FROM journal_entries \n      WHERE user_id = $1 \n        AND date >= $2 \n        AND date <= $3\n      ORDER BY date DESC\n    `, [\n            userId,\n            startDate.toISOString().split(\"T\")[0],\n            endDate.toISOString().split(\"T\")[0]\n        ]);\n        // Get course progress\n        const courseResult = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT * FROM course_progress WHERE user_id = $1\n    `, [\n            userId\n        ]);\n        const checklists = checklistResult.rows;\n        const journals = journalResult.rows;\n        const courseProgress = courseResult.rows[0];\n        return {\n            checklists,\n            journals,\n            courseProgress,\n            summary: calculateSummaryStats(checklists, journals, courseProgress)\n        };\n    } catch (error) {\n        console.error(\"Error getting user analytics:\", error);\n        return {\n            checklists: [],\n            journals: [],\n            courseProgress: null,\n            summary: getEmptyStats()\n        };\n    }\n};\n/**\n * Calculate summary statistics from real data\n */ const calculateSummaryStats = (checklists, journals, courseProgress)=>{\n    const totalDays = 30;\n    // Checklist statistics\n    const checklistDays = checklists.length;\n    const checklistCompletionRate = Math.round(checklistDays / totalDays * 100);\n    // Calculate average checklist score\n    let totalChecklistScore = 0;\n    let checklistItemCount = 0;\n    checklists.forEach((checklist)=>{\n        if (checklist.items) {\n            const completedItems = Object.values(checklist.items).filter(Boolean).length;\n            totalChecklistScore += completedItems;\n            checklistItemCount++;\n        }\n    });\n    const avgChecklistScore = checklistItemCount > 0 ? (totalChecklistScore / checklistItemCount).toFixed(1) : \"0.0\";\n    // Calculate average confidence\n    const confidenceRatings = checklists.map((c)=>c.confidence_rating).filter((rating)=>rating && rating > 0);\n    const avgConfidence = confidenceRatings.length > 0 ? (confidenceRatings.reduce((sum, rating)=>sum + rating, 0) / confidenceRatings.length).toFixed(1) : \"0.0\";\n    // Journal statistics\n    const journalDays = journals.length;\n    const journalCompletionRate = Math.round(journalDays / totalDays * 100);\n    // Calculate average journal length\n    const journalLengths = journals.map((j)=>j.content ? j.content.length : 0).filter((length)=>length > 0);\n    const avgJournalLength = journalLengths.length > 0 ? Math.round(journalLengths.reduce((sum, length)=>sum + length, 0) / journalLengths.length) : 0;\n    // Combined statistics\n    const bothCompletedDays = checklists.filter((checklist)=>journals.some((journal)=>journal.date.toISOString().split(\"T\")[0] === checklist.date.toISOString().split(\"T\")[0])).length;\n    const goalAchievementRate = Math.round(bothCompletedDays / totalDays * 100);\n    // Course statistics\n    const courseStats = courseProgress ? {\n        currentModule: courseProgress.current_module,\n        currentLesson: courseProgress.current_lesson,\n        completedLessons: courseProgress.completed_lessons?.length || 0,\n        completedModules: courseProgress.completed_modules?.length || 0,\n        progress: courseProgress.progress || 0,\n        achievements: courseProgress.achievements?.length || 0\n    } : {\n        currentModule: 1,\n        currentLesson: 1,\n        completedLessons: 0,\n        completedModules: 0,\n        progress: 0,\n        achievements: 0\n    };\n    // Calculate streak\n    const streak = calculateStreak(checklists.map((c)=>c.date.toISOString().split(\"T\")[0]));\n    return {\n        checklist: {\n            totalDays: checklistDays,\n            completionRate: checklistCompletionRate,\n            avgScore: avgChecklistScore,\n            avgConfidence: avgConfidence\n        },\n        journal: {\n            totalDays: journalDays,\n            completionRate: journalCompletionRate,\n            avgLength: avgJournalLength\n        },\n        combined: {\n            bothCompletedDays,\n            goalAchievementRate,\n            streak\n        },\n        course: courseStats\n    };\n};\n/**\n * Calculate actual streak from dates\n */ const calculateStreak = (dates)=>{\n    if (!dates || dates.length === 0) return 0;\n    const sortedDates = dates.sort().reverse();\n    let streak = 0;\n    const today = new Date().toISOString().split(\"T\")[0];\n    // Check if today or yesterday has activity\n    const latestDate = sortedDates[0];\n    const daysDiff = Math.floor((new Date(today) - new Date(latestDate)) / (1000 * 60 * 60 * 24));\n    if (daysDiff > 1) return 0 // Streak broken\n    ;\n    // Count consecutive days\n    for(let i = 0; i < sortedDates.length; i++){\n        const currentDate = new Date(sortedDates[i]);\n        const expectedDate = new Date(today);\n        expectedDate.setDate(expectedDate.getDate() - i);\n        if (currentDate.toISOString().split(\"T\")[0] === expectedDate.toISOString().split(\"T\")[0]) {\n            streak++;\n        } else {\n            break;\n        }\n    }\n    return streak;\n};\n/**\n * Get empty stats structure\n */ const getEmptyStats = ()=>({\n        checklist: {\n            totalDays: 0,\n            completionRate: 0,\n            avgScore: \"0.0\",\n            avgConfidence: \"0.0\"\n        },\n        journal: {\n            totalDays: 0,\n            completionRate: 0,\n            avgLength: 0\n        },\n        combined: {\n            bothCompletedDays: 0,\n            goalAchievementRate: 0,\n            streak: 0\n        },\n        course: {\n            currentModule: 1,\n            currentLesson: 1,\n            completedLessons: 0,\n            completedModules: 0,\n            progress: 0,\n            achievements: 0\n        }\n    });\n/**\n * Get performance trend data\n */ const getPerformanceTrend = async (userId = 1, days = 30)=>{\n    try {\n        const endDate = new Date();\n        const startDate = new Date(endDate.getTime() - days * 24 * 60 * 60 * 1000);\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT \n        c.date,\n        c.items,\n        c.confidence_rating,\n        CASE WHEN j.content IS NOT NULL AND LENGTH(j.content) > 0 THEN 1 ELSE 0 END as has_journal\n      FROM checklists c\n      LEFT JOIN journal_entries j ON c.date = j.date AND c.user_id = j.user_id\n      WHERE c.user_id = $1 \n        AND c.date >= $2 \n        AND c.date <= $3\n      ORDER BY c.date ASC\n    `, [\n            userId,\n            startDate.toISOString().split(\"T\")[0],\n            endDate.toISOString().split(\"T\")[0]\n        ]);\n        return result.rows.map((row)=>({\n                date: new Date(row.date).toLocaleDateString(\"en-US\", {\n                    month: \"short\",\n                    day: \"numeric\"\n                }),\n                checklist: row.items ? Object.values(row.items).filter(Boolean).length : 0,\n                journal: row.has_journal,\n                confidence: row.confidence_rating || 0\n            }));\n    } catch (error) {\n        console.error(\"Error getting performance trend:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/analytics.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.js":
/*!***********************!*\
  !*** ./src/lib/db.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getClient: () => (/* binding */ getClient),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Database connection utility for Neon PostgreSQL\n * Simple, minimal setup for cloud storage\n */ \n// Create a connection pool for better performance\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    connectionString: process.env.DATABASE_URL,\n    ssl: {\n        rejectUnauthorized: false\n    },\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n/**\n * Execute a database query\n * @param {string} text - SQL query\n * @param {Array} params - Query parameters\n * @returns {Promise} Query result\n */ const query = async (text, params)=>{\n    const start = Date.now();\n    try {\n        const res = await pool.query(text, params);\n        const duration = Date.now() - start;\n        console.log(\"Executed query\", {\n            text,\n            duration,\n            rows: res.rowCount\n        });\n        return res;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n};\n/**\n * Get a client from the pool for transactions\n * @returns {Promise} Database client\n */ const getClient = async ()=>{\n    return await pool.connect();\n};\n/**\n * Initialize database tables\n * Creates tables if they don't exist\n */ const initializeDatabase = async ()=>{\n    try {\n        // Users table for basic user management\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE,\n        username VARCHAR(100),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Checklists table\n        await query(`\n      CREATE TABLE IF NOT EXISTS checklists (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        items JSONB DEFAULT '{}',\n        notes JSONB DEFAULT '{}',\n        confidence_rating INTEGER DEFAULT 5,\n        session_start_time TIMESTAMP,\n        is_session_active BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Journal entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        content TEXT,\n        tags JSONB DEFAULT '[]',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Course progress table\n        await query(`\n      CREATE TABLE IF NOT EXISTS course_progress (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        current_module INTEGER DEFAULT 1,\n        current_lesson INTEGER DEFAULT 1,\n        completed_lessons JSONB DEFAULT '[]',\n        completed_modules JSONB DEFAULT '[]',\n        progress INTEGER DEFAULT 0,\n        achievements JSONB DEFAULT '[]',\n        quiz_scores JSONB DEFAULT '{}',\n        bookmarks JSONB DEFAULT '[]',\n        notes JSONB DEFAULT '{}',\n        last_accessed TIMESTAMP,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        // User preferences table\n        await query(`\n      CREATE TABLE IF NOT EXISTS user_preferences (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        theme VARCHAR(20) DEFAULT 'light',\n        language VARCHAR(10) DEFAULT 'en',\n        notifications BOOLEAN DEFAULT TRUE,\n        auto_save BOOLEAN DEFAULT TRUE,\n        confidence_rating BOOLEAN DEFAULT TRUE,\n        session_timer BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        console.log(\"Database tables initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing database:\", error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();