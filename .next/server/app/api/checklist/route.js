"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/checklist/route";
exports.ids = ["app/api/checklist/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchecklist%2Froute&page=%2Fapi%2Fchecklist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecklist%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchecklist%2Froute&page=%2Fapi%2Fchecklist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecklist%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_checklist_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/checklist/route.js */ \"(rsc)/./src/app/api/checklist/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/checklist/route\",\n        pathname: \"/api/checklist\",\n        filename: \"route\",\n        bundlePath: \"app/api/checklist/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/api/checklist/route.js\",\n    nextConfigOutput,\n    userland: _Users_edwardaver_Desktop_Limitless_Checklist_Project_src_app_api_checklist_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/checklist/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchecklist%2Froute&page=%2Fapi%2Fchecklist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecklist%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/checklist/route.js":
/*!****************************************!*\
  !*** ./src/app/api/checklist/route.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_cloudStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/cloudStorage */ \"(rsc)/./src/lib/cloudStorage.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/**\n * API Routes for checklist operations\n * Handles saving and loading checklist data from cloud\n */ \n\nasync function POST(request) {\n    try {\n        const { date, data } = await request.json();\n        if (!date || !data) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                error: \"Date and data are required\"\n            }, {\n                status: 400\n            });\n        }\n        const success = await (0,_lib_cloudStorage__WEBPACK_IMPORTED_MODULE_0__.saveChecklistToCloud)(date, data);\n        if (success) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: true\n            });\n        } else {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                error: \"Failed to save checklist\"\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Checklist save error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const date = searchParams.get(\"date\");\n        if (!date) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                error: \"Date parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        const data = await (0,_lib_cloudStorage__WEBPACK_IMPORTED_MODULE_0__.loadChecklistFromCloud)(date);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            data: data || null\n        });\n    } catch (error) {\n        console.error(\"Checklist load error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/checklist/route.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/cloudStorage.js":
/*!*********************************!*\
  !*** ./src/lib/cloudStorage.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureUser: () => (/* binding */ ensureUser),\n/* harmony export */   getAllChecklistDatesFromCloud: () => (/* binding */ getAllChecklistDatesFromCloud),\n/* harmony export */   getAllJournalDatesFromCloud: () => (/* binding */ getAllJournalDatesFromCloud),\n/* harmony export */   loadChecklistFromCloud: () => (/* binding */ loadChecklistFromCloud),\n/* harmony export */   loadCourseProgressFromCloud: () => (/* binding */ loadCourseProgressFromCloud),\n/* harmony export */   loadJournalFromCloud: () => (/* binding */ loadJournalFromCloud),\n/* harmony export */   saveChecklistToCloud: () => (/* binding */ saveChecklistToCloud),\n/* harmony export */   saveCourseProgressToCloud: () => (/* binding */ saveCourseProgressToCloud),\n/* harmony export */   saveJournalToCloud: () => (/* binding */ saveJournalToCloud)\n/* harmony export */ });\n/* harmony import */ var _db_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./db.js */ \"(rsc)/./src/lib/db.js\");\n/**\n * Cloud Storage Service for Neon PostgreSQL\n * Handles all database operations with simple, clean API\n */ \n// Simple user ID for now (in production, use proper authentication)\nconst DEFAULT_USER_ID = 1;\n/**\n * Ensure user exists in database\n */ const ensureUser = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT id FROM users WHERE id = $1\", [\n            userId\n        ]);\n        if (result.rows.length === 0) {\n            await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"INSERT INTO users (id, username) VALUES ($1, $2) ON CONFLICT (id) DO NOTHING\", [\n                userId,\n                \"default_user\"\n            ]);\n        }\n        return userId;\n    } catch (error) {\n        console.error(\"Error ensuring user:\", error);\n        throw error;\n    }\n};\n/**\n * Save checklist data to cloud\n */ const saveChecklistToCloud = async (date, data, userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO checklists (user_id, date, items, notes, confidence_rating, session_start_time, is_session_active, updated_at)\n      VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id, date) \n      DO UPDATE SET \n        items = $3,\n        notes = $4,\n        confidence_rating = $5,\n        session_start_time = $6,\n        is_session_active = $7,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            date,\n            JSON.stringify(data.items || {}),\n            JSON.stringify(data.notes || {}),\n            data.confidenceRating || 5,\n            data.sessionStartTime || null,\n            data.isSessionActive || false\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving checklist to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load checklist data from cloud\n */ const loadChecklistFromCloud = async (date, userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM checklists WHERE user_id = $1 AND date = $2\", [\n            userId,\n            date\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            items: row.items || {},\n            notes: row.notes || {},\n            confidenceRating: row.confidence_rating || 5,\n            sessionStartTime: row.session_start_time,\n            isSessionActive: row.is_session_active || false,\n            lastUpdated: row.updated_at\n        };\n    } catch (error) {\n        console.error(\"Error loading checklist from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Save journal entry to cloud\n */ const saveJournalToCloud = async (date, content, tags = [], userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO journal_entries (user_id, date, content, tags, updated_at)\n      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id, date)\n      DO UPDATE SET \n        content = $3,\n        tags = $4,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            date,\n            content,\n            JSON.stringify(tags)\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving journal to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load journal entry from cloud\n */ const loadJournalFromCloud = async (date, userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM journal_entries WHERE user_id = $1 AND date = $2\", [\n            userId,\n            date\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            content: row.content || \"\",\n            tags: row.tags || [],\n            lastUpdated: row.updated_at\n        };\n    } catch (error) {\n        console.error(\"Error loading journal from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Save course progress to cloud\n */ const saveCourseProgressToCloud = async (courseData, userId = DEFAULT_USER_ID)=>{\n    try {\n        await ensureUser(userId);\n        await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO course_progress (\n        user_id, current_module, current_lesson, completed_lessons, \n        completed_modules, progress, achievements, quiz_scores, \n        bookmarks, notes, last_accessed, updated_at\n      )\n      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)\n      ON CONFLICT (user_id)\n      DO UPDATE SET \n        current_module = $2,\n        current_lesson = $3,\n        completed_lessons = $4,\n        completed_modules = $5,\n        progress = $6,\n        achievements = $7,\n        quiz_scores = $8,\n        bookmarks = $9,\n        notes = $10,\n        last_accessed = $11,\n        updated_at = CURRENT_TIMESTAMP\n    `, [\n            userId,\n            courseData.currentModule,\n            courseData.currentLesson,\n            JSON.stringify(courseData.completedLessons),\n            JSON.stringify(courseData.completedModules),\n            courseData.progress,\n            JSON.stringify(courseData.achievements),\n            JSON.stringify(courseData.quizScores),\n            JSON.stringify(courseData.bookmarks),\n            JSON.stringify(courseData.notes),\n            courseData.lastAccessed\n        ]);\n        return true;\n    } catch (error) {\n        console.error(\"Error saving course progress to cloud:\", error);\n        return false;\n    }\n};\n/**\n * Load course progress from cloud\n */ const loadCourseProgressFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM course_progress WHERE user_id = $1\", [\n            userId\n        ]);\n        if (result.rows.length === 0) {\n            return null;\n        }\n        const row = result.rows[0];\n        return {\n            currentModule: row.current_module,\n            currentLesson: row.current_lesson,\n            completedLessons: row.completed_lessons || [],\n            completedModules: row.completed_modules || [],\n            progress: row.progress || 0,\n            achievements: row.achievements || [],\n            quizScores: row.quiz_scores || {},\n            bookmarks: row.bookmarks || [],\n            notes: row.notes || {},\n            lastAccessed: row.last_accessed\n        };\n    } catch (error) {\n        console.error(\"Error loading course progress from cloud:\", error);\n        return null;\n    }\n};\n/**\n * Get all checklist dates from cloud\n */ const getAllChecklistDatesFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT date FROM checklists WHERE user_id = $1 ORDER BY date DESC\", [\n            userId\n        ]);\n        return result.rows.map((row)=>row.date.toISOString().split(\"T\")[0]);\n    } catch (error) {\n        console.error(\"Error getting checklist dates from cloud:\", error);\n        return [];\n    }\n};\n/**\n * Get all journal dates from cloud\n */ const getAllJournalDatesFromCloud = async (userId = DEFAULT_USER_ID)=>{\n    try {\n        const result = await (0,_db_js__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT date FROM journal_entries WHERE user_id = $1 ORDER BY date DESC\", [\n            userId\n        ]);\n        return result.rows.map((row)=>row.date.toISOString().split(\"T\")[0]);\n    } catch (error) {\n        console.error(\"Error getting journal dates from cloud:\", error);\n        return [];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/cloudStorage.js\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.js":
/*!***********************!*\
  !*** ./src/lib/db.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getClient: () => (/* binding */ getClient),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   query: () => (/* binding */ query)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * Database connection utility for Neon PostgreSQL\n * Simple, minimal setup for cloud storage\n */ \n// Create a connection pool for better performance\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    connectionString: process.env.DATABASE_URL,\n    ssl: {\n        rejectUnauthorized: false\n    },\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n/**\n * Execute a database query\n * @param {string} text - SQL query\n * @param {Array} params - Query parameters\n * @returns {Promise} Query result\n */ const query = async (text, params)=>{\n    const start = Date.now();\n    try {\n        const res = await pool.query(text, params);\n        const duration = Date.now() - start;\n        console.log(\"Executed query\", {\n            text,\n            duration,\n            rows: res.rowCount\n        });\n        return res;\n    } catch (error) {\n        console.error(\"Database query error:\", error);\n        throw error;\n    }\n};\n/**\n * Get a client from the pool for transactions\n * @returns {Promise} Database client\n */ const getClient = async ()=>{\n    return await pool.connect();\n};\n/**\n * Initialize database tables\n * Creates tables if they don't exist\n */ const initializeDatabase = async ()=>{\n    try {\n        // Users table for basic user management\n        await query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id SERIAL PRIMARY KEY,\n        email VARCHAR(255) UNIQUE,\n        username VARCHAR(100),\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Checklists table\n        await query(`\n      CREATE TABLE IF NOT EXISTS checklists (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        items JSONB DEFAULT '{}',\n        notes JSONB DEFAULT '{}',\n        confidence_rating INTEGER DEFAULT 5,\n        session_start_time TIMESTAMP,\n        is_session_active BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Journal entries table\n        await query(`\n      CREATE TABLE IF NOT EXISTS journal_entries (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        date DATE NOT NULL,\n        content TEXT,\n        tags JSONB DEFAULT '[]',\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id, date)\n      )\n    `);\n        // Course progress table\n        await query(`\n      CREATE TABLE IF NOT EXISTS course_progress (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        current_module INTEGER DEFAULT 1,\n        current_lesson INTEGER DEFAULT 1,\n        completed_lessons JSONB DEFAULT '[]',\n        completed_modules JSONB DEFAULT '[]',\n        progress INTEGER DEFAULT 0,\n        achievements JSONB DEFAULT '[]',\n        quiz_scores JSONB DEFAULT '{}',\n        bookmarks JSONB DEFAULT '[]',\n        notes JSONB DEFAULT '{}',\n        last_accessed TIMESTAMP,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        // User preferences table\n        await query(`\n      CREATE TABLE IF NOT EXISTS user_preferences (\n        id SERIAL PRIMARY KEY,\n        user_id INTEGER REFERENCES users(id),\n        theme VARCHAR(20) DEFAULT 'light',\n        language VARCHAR(10) DEFAULT 'en',\n        notifications BOOLEAN DEFAULT TRUE,\n        auto_save BOOLEAN DEFAULT TRUE,\n        confidence_rating BOOLEAN DEFAULT TRUE,\n        session_timer BOOLEAN DEFAULT FALSE,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        UNIQUE(user_id)\n      )\n    `);\n        console.log(\"Database tables initialized successfully\");\n        return true;\n    } catch (error) {\n        console.error(\"Error initializing database:\", error);\n        throw error;\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fchecklist%2Froute&page=%2Fapi%2Fchecklist%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecklist%2Froute.js&appDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fedwardaver%2FDesktop%2FLimitless%20Checklist%20Project&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();