"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decimal.js-light";
exports.ids = ["vendor-chunks/decimal.js-light"];
exports.modules = {

/***/ "(ssr)/./node_modules/decimal.js-light/decimal.mjs":
/*!***************************************************!*\
  !*** ./node_modules/decimal.js-light/decimal.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decimal: () => (/* binding */ Decimal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  decimal.js-light v2.5.1\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js-light\r\n *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n *  MIT Expat Licence\r\n */ // ------------------------------------  EDITABLE DEFAULTS  ------------------------------------- //\n// The limit on the value of `precision`, and on the value of the first argument to\n// `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\nvar MAX_DIGITS = 1e9, // The initial configuration properties of the Decimal constructor.\ndefaults = {\n    // These values must be integers within the stated ranges (inclusive).\n    // Most of these values can be changed during run-time using `Decimal.config`.\n    // The maximum number of significant digits of the result of a calculation or base conversion.\n    // E.g. `Decimal.config({ precision: 20 });`\n    precision: 20,\n    // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\n    // `toFixed`, `toPrecision` and `toSignificantDigits`.\n    //\n    // ROUND_UP         0 Away from zero.\n    // ROUND_DOWN       1 Towards zero.\n    // ROUND_CEIL       2 Towards +Infinity.\n    // ROUND_FLOOR      3 Towards -Infinity.\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n    //\n    // E.g.\n    // `Decimal.rounding = 4;`\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n    rounding: 4,\n    // The exponent value at and beneath which `toString` returns exponential notation.\n    // JavaScript numbers: -7\n    toExpNeg: -7,\n    // The exponent value at and above which `toString` returns exponential notation.\n    // JavaScript numbers: 21\n    toExpPos: 21,\n    // The natural logarithm of 10.\n    // 115 digits\n    LN10: \"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286\"\n}, // ------------------------------------ END OF EDITABLE DEFAULTS -------------------------------- //\nDecimal, external = true, decimalError = \"[DecimalError] \", invalidArgument = decimalError + \"Invalid argument: \", exponentOutOfRange = decimalError + \"Exponent out of range: \", mathfloor = Math.floor, mathpow = Math.pow, isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i, ONE, BASE = 1e7, LOG_BASE = 7, MAX_SAFE_INTEGER = 9007199254740991, MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE), // Decimal.prototype object\nP = {};\n// Decimal prototype methods\n/*\r\n *  absoluteValue                       abs\r\n *  comparedTo                          cmp\r\n *  decimalPlaces                       dp\r\n *  dividedBy                           div\r\n *  dividedToIntegerBy                  idiv\r\n *  equals                              eq\r\n *  exponent\r\n *  greaterThan                         gt\r\n *  greaterThanOrEqualTo                gte\r\n *  isInteger                           isint\r\n *  isNegative                          isneg\r\n *  isPositive                          ispos\r\n *  isZero\r\n *  lessThan                            lt\r\n *  lessThanOrEqualTo                   lte\r\n *  logarithm                           log\r\n *  minus                               sub\r\n *  modulo                              mod\r\n *  naturalExponential                  exp\r\n *  naturalLogarithm                    ln\r\n *  negated                             neg\r\n *  plus                                add\r\n *  precision                           sd\r\n *  squareRoot                          sqrt\r\n *  times                               mul\r\n *  toDecimalPlaces                     todp\r\n *  toExponential\r\n *  toFixed\r\n *  toInteger                           toint\r\n *  toNumber\r\n *  toPower                             pow\r\n *  toPrecision\r\n *  toSignificantDigits                 tosd\r\n *  toString\r\n *  valueOf                             val\r\n */ /*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */ P.absoluteValue = P.abs = function() {\n    var x = new this.constructor(this);\n    if (x.s) x.s = 1;\n    return x;\n};\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value\r\n *\r\n */ P.comparedTo = P.cmp = function(y) {\n    var i, j, xdL, ydL, x = this;\n    y = new x.constructor(y);\n    // Signs differ?\n    if (x.s !== y.s) return x.s || -y.s;\n    // Compare exponents.\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\n    xdL = x.d.length;\n    ydL = y.d.length;\n    // Compare digit by digit.\n    for(i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i){\n        if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\n    }\n    // Compare lengths.\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\n};\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */ P.decimalPlaces = P.dp = function() {\n    var x = this, w = x.d.length - 1, dp = (w - x.e) * LOG_BASE;\n    // Subtract the number of trailing zeros of the last word.\n    w = x.d[w];\n    if (w) for(; w % 10 == 0; w /= 10)dp--;\n    return dp < 0 ? 0 : dp;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.dividedBy = P.div = function(y) {\n    return divide(this, new this.constructor(y));\n};\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, truncated to `precision` significant digits.\r\n *\r\n */ P.dividedToIntegerBy = P.idiv = function(y) {\n    var x = this, Ctor = x.constructor;\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\n};\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */ P.equals = P.eq = function(y) {\n    return !this.cmp(y);\n};\n/*\r\n * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n *\r\n */ P.exponent = function() {\n    return getBase10Exponent(this);\n};\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */ P.greaterThan = P.gt = function(y) {\n    return this.cmp(y) > 0;\n};\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */ P.greaterThanOrEqualTo = P.gte = function(y) {\n    return this.cmp(y) >= 0;\n};\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */ P.isInteger = P.isint = function() {\n    return this.e > this.d.length - 2;\n};\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */ P.isNegative = P.isneg = function() {\n    return this.s < 0;\n};\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */ P.isPositive = P.ispos = function() {\n    return this.s > 0;\n};\n/*\r\n * Return true if the value of this Decimal is 0, otherwise return false.\r\n *\r\n */ P.isZero = function() {\n    return this.s === 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */ P.lessThan = P.lt = function(y) {\n    return this.cmp(y) < 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */ P.lessThanOrEqualTo = P.lte = function(y) {\n    return this.cmp(y) < 1;\n};\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n * `precision` significant digits.\r\n *\r\n * If no base is specified, return log[10](x).\r\n *\r\n * log[base](x) = ln(x) / ln(base)\r\n *\r\n * The maximum error of the result is 1 ulp (unit in the last place).\r\n *\r\n * [base] {number|string|Decimal} The base of the logarithm.\r\n *\r\n */ P.logarithm = P.log = function(base) {\n    var r, x = this, Ctor = x.constructor, pr = Ctor.precision, wpr = pr + 5;\n    // Default base is 10.\n    if (base === void 0) {\n        base = new Ctor(10);\n    } else {\n        base = new Ctor(base);\n        // log[-b](x) = NaN\n        // log[0](x)  = NaN\n        // log[1](x)  = NaN\n        if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + \"NaN\");\n    }\n    // log[b](-x) = NaN\n    // log[b](0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // log[b](1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    external = false;\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.minus = P.sub = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.modulo = P.mod = function(y) {\n    var q, x = this, Ctor = x.constructor, pr = Ctor.precision;\n    y = new Ctor(y);\n    // x % 0 = NaN\n    if (!y.s) throw Error(decimalError + \"NaN\");\n    // Return x if x is 0.\n    if (!x.s) return round(new Ctor(x), pr);\n    // Prevent rounding of intermediate calculations.\n    external = false;\n    q = divide(x, y, 0, 1).times(y);\n    external = true;\n    return x.minus(q);\n};\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.naturalExponential = P.exp = function() {\n    return exp(this);\n};\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * truncated to `precision` significant digits.\r\n *\r\n */ P.naturalLogarithm = P.ln = function() {\n    return ln(this);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */ P.negated = P.neg = function() {\n    var x = new this.constructor(this);\n    x.s = -x.s || 0;\n    return x;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.plus = P.add = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\n};\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */ P.precision = P.sd = function(z) {\n    var e, sd, w, x = this;\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n    e = getBase10Exponent(x) + 1;\n    w = x.d.length - 1;\n    sd = w * LOG_BASE + 1;\n    w = x.d[w];\n    // If non-zero...\n    if (w) {\n        // Subtract the number of trailing zeros of the last word.\n        for(; w % 10 == 0; w /= 10)sd--;\n        // Add the number of digits of the first word.\n        for(w = x.d[0]; w >= 10; w /= 10)sd++;\n    }\n    return z && e > sd ? e : sd;\n};\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.squareRoot = P.sqrt = function() {\n    var e, n, pr, r, s, t, wpr, x = this, Ctor = x.constructor;\n    // Negative or zero?\n    if (x.s < 1) {\n        if (!x.s) return new Ctor(0);\n        // sqrt(-x) = NaN\n        throw Error(decimalError + \"NaN\");\n    }\n    e = getBase10Exponent(x);\n    external = false;\n    // Initial estimate.\n    s = Math.sqrt(+x);\n    // Math.sqrt underflow/overflow?\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n    if (s == 0 || s == 1 / 0) {\n        n = digitsToString(x.d);\n        if ((n.length + e) % 2 == 0) n += \"0\";\n        s = Math.sqrt(n);\n        e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n        if (s == 1 / 0) {\n            n = \"5e\" + e;\n        } else {\n            n = s.toExponential();\n            n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n        }\n        r = new Ctor(n);\n    } else {\n        r = new Ctor(s.toString());\n    }\n    pr = Ctor.precision;\n    s = wpr = pr + 3;\n    // Newton-Raphson iteration.\n    for(;;){\n        t = r;\n        r = t.plus(divide(x, t, wpr + 2)).times(0.5);\n        if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\n            n = n.slice(wpr - 3, wpr + 1);\n            // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n            // 4999, i.e. approaching a rounding boundary, continue the iteration.\n            if (s == wpr && n == \"4999\") {\n                // On the first iteration only, check to see if rounding up gives the exact result as the\n                // nines may infinitely repeat.\n                round(t, pr + 1, 0);\n                if (t.times(t).eq(x)) {\n                    r = t;\n                    break;\n                }\n            } else if (n != \"9999\") {\n                break;\n            }\n            wpr += 4;\n        }\n    }\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.times = P.mul = function(y) {\n    var carry, e, i, k, r, rL, t, xdL, ydL, x = this, Ctor = x.constructor, xd = x.d, yd = (y = new Ctor(y)).d;\n    // Return 0 if either is 0.\n    if (!x.s || !y.s) return new Ctor(0);\n    y.s *= x.s;\n    e = x.e + y.e;\n    xdL = xd.length;\n    ydL = yd.length;\n    // Ensure xd points to the longer array.\n    if (xdL < ydL) {\n        r = xd;\n        xd = yd;\n        yd = r;\n        rL = xdL;\n        xdL = ydL;\n        ydL = rL;\n    }\n    // Initialise the result array with zeros.\n    r = [];\n    rL = xdL + ydL;\n    for(i = rL; i--;)r.push(0);\n    // Multiply!\n    for(i = ydL; --i >= 0;){\n        carry = 0;\n        for(k = xdL + i; k > i;){\n            t = r[k] + yd[i] * xd[k - i - 1] + carry;\n            r[k--] = t % BASE | 0;\n            carry = t / BASE | 0;\n        }\n        r[k] = (r[k] + carry) % BASE | 0;\n    }\n    // Remove trailing zeros.\n    for(; !r[--rL];)r.pop();\n    if (carry) ++e;\n    else r.shift();\n    y.d = r;\n    y.e = e;\n    return external ? round(y, Ctor.precision) : y;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toDecimalPlaces = P.todp = function(dp, rm) {\n    var x = this, Ctor = x.constructor;\n    x = new Ctor(x);\n    if (dp === void 0) return x;\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toExponential = function(dp, rm) {\n    var str, x = this, Ctor = x.constructor;\n    if (dp === void 0) {\n        str = toString(x, true);\n    } else {\n        checkInt32(dp, 0, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), dp + 1, rm);\n        str = toString(x, true, dp + 1);\n    }\n    return str;\n};\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */ P.toFixed = function(dp, rm) {\n    var str, y, x = this, Ctor = x.constructor;\n    if (dp === void 0) return toString(x);\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\n    // To determine whether to add the minus sign look at the value before it was rounded,\n    // i.e. look at `x` rather than `y`.\n    return x.isneg() && !x.isZero() ? \"-\" + str : str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */ P.toInteger = P.toint = function() {\n    var x = this, Ctor = x.constructor;\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\n};\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n *\r\n */ P.toNumber = function() {\n    return +this;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n * truncated to `precision` significant digits.\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * The maximum error is 1 ulp (unit in last place).\r\n *\r\n * y {number|string|Decimal} The power to which to raise this Decimal.\r\n *\r\n */ P.toPower = P.pow = function(y) {\n    var e, k, pr, r, sign, yIsInt, x = this, Ctor = x.constructor, guard = 12, yn = +(y = new Ctor(y));\n    // pow(x, 0) = 1\n    if (!y.s) return new Ctor(ONE);\n    x = new Ctor(x);\n    // pow(0, y > 0) = 0\n    // pow(0, y < 0) = Infinity\n    if (!x.s) {\n        if (y.s < 1) throw Error(decimalError + \"Infinity\");\n        return x;\n    }\n    // pow(1, y) = 1\n    if (x.eq(ONE)) return x;\n    pr = Ctor.precision;\n    // pow(x, 1) = x\n    if (y.eq(ONE)) return round(x, pr);\n    e = y.e;\n    k = y.d.length - 1;\n    yIsInt = e >= k;\n    sign = x.s;\n    if (!yIsInt) {\n        // pow(x < 0, y non-integer) = NaN\n        if (sign < 0) throw Error(decimalError + \"NaN\");\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n        r = new Ctor(ONE);\n        // Max k of 9007199254740991 takes 53 loop iterations.\n        // Maximum digits array length; leaves [28, 34] guard digits.\n        e = Math.ceil(pr / LOG_BASE + 4);\n        external = false;\n        for(;;){\n            if (k % 2) {\n                r = r.times(x);\n                truncate(r.d, e);\n            }\n            k = mathfloor(k / 2);\n            if (k === 0) break;\n            x = x.times(x);\n            truncate(x.d, e);\n        }\n        external = true;\n        return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\n    }\n    // Result is negative if x is negative and the last digit of integer y is odd.\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\n    x.s = 1;\n    external = false;\n    r = y.times(ln(x, pr + guard));\n    external = true;\n    r = exp(r);\n    r.s = sign;\n    return r;\n};\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toPrecision = function(sd, rm) {\n    var e, str, x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        e = getBase10Exponent(x);\n        str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), sd, rm);\n        e = getBase10Exponent(x);\n        str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\n    }\n    return str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toSignificantDigits = P.tosd = function(sd, rm) {\n    var x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        sd = Ctor.precision;\n        rm = Ctor.rounding;\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n    }\n    return round(new Ctor(x), sd, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */ P.toString = P.valueOf = P.val = P.toJSON = P[Symbol.for(\"nodejs.util.inspect.custom\")] = function() {\n    var x = this, e = getBase10Exponent(x), Ctor = x.constructor;\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n};\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n/*\r\n *  add                 P.minus, P.plus\r\n *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n *  exp                 P.exp, P.pow\r\n *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n *                      P.toString, divide, round, toString, exp, ln\r\n *  getLn10             P.log, ln\r\n *  getZeroString       digitsToString, toString\r\n *  ln                  P.log, P.ln, P.pow, exp\r\n *  parseDecimal        Decimal\r\n *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n *                      divide, getLn10, exp, ln\r\n *  subtract            P.minus, P.plus\r\n *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n *  truncate            P.pow\r\n *\r\n *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n */ function add(x, y) {\n    var carry, d, e, i, k, len, xd, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // If either is zero...\n    if (!x.s || !y.s) {\n        // Return x if y is zero.\n        // Return y if y is non-zero.\n        if (!y.s) y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are finite, non-zero numbers with the same sign.\n    k = x.e;\n    e = y.e;\n    xd = xd.slice();\n    i = k - e;\n    // If base 1e7 exponents differ...\n    if (i) {\n        if (i < 0) {\n            d = xd;\n            i = -i;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = k;\n            len = xd.length;\n        }\n        // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n        k = Math.ceil(pr / LOG_BASE);\n        len = k > len ? k + 1 : len + 1;\n        if (i > len) {\n            i = len;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n        d.reverse();\n        for(; i--;)d.push(0);\n        d.reverse();\n    }\n    len = xd.length;\n    i = yd.length;\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n    if (len - i < 0) {\n        i = len;\n        d = yd;\n        yd = xd;\n        xd = d;\n    }\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n    for(carry = 0; i;){\n        carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n        xd[i] %= BASE;\n    }\n    if (carry) {\n        xd.unshift(carry);\n        ++e;\n    }\n    // Remove trailing zeros.\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n    for(len = xd.length; xd[--len] == 0;)xd.pop();\n    y.d = xd;\n    y.e = e;\n    return external ? round(y, pr) : y;\n}\nfunction checkInt32(i, min, max) {\n    if (i !== ~~i || i < min || i > max) {\n        throw Error(invalidArgument + i);\n    }\n}\nfunction digitsToString(d) {\n    var i, k, ws, indexOfLastWord = d.length - 1, str = \"\", w = d[0];\n    if (indexOfLastWord > 0) {\n        str += w;\n        for(i = 1; i < indexOfLastWord; i++){\n            ws = d[i] + \"\";\n            k = LOG_BASE - ws.length;\n            if (k) str += getZeroString(k);\n            str += ws;\n        }\n        w = d[i];\n        ws = w + \"\";\n        k = LOG_BASE - ws.length;\n        if (k) str += getZeroString(k);\n    } else if (w === 0) {\n        return \"0\";\n    }\n    // Remove trailing zeros of last w.\n    for(; w % 10 === 0;)w /= 10;\n    return str + w;\n}\nvar divide = function() {\n    // Assumes non-zero x and k, and hence non-zero result.\n    function multiplyInteger(x, k) {\n        var temp, carry = 0, i = x.length;\n        for(x = x.slice(); i--;){\n            temp = x[i] * k + carry;\n            x[i] = temp % BASE | 0;\n            carry = temp / BASE | 0;\n        }\n        if (carry) x.unshift(carry);\n        return x;\n    }\n    function compare(a, b, aL, bL) {\n        var i, r;\n        if (aL != bL) {\n            r = aL > bL ? 1 : -1;\n        } else {\n            for(i = r = 0; i < aL; i++){\n                if (a[i] != b[i]) {\n                    r = a[i] > b[i] ? 1 : -1;\n                    break;\n                }\n            }\n        }\n        return r;\n    }\n    function subtract(a, b, aL) {\n        var i = 0;\n        // Subtract b from a.\n        for(; aL--;){\n            a[aL] -= i;\n            i = a[aL] < b[aL] ? 1 : 0;\n            a[aL] = i * BASE + a[aL] - b[aL];\n        }\n        // Remove leading zeros.\n        for(; !a[0] && a.length > 1;)a.shift();\n    }\n    return function(x, y, pr, dp) {\n        var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz, Ctor = x.constructor, sign = x.s == y.s ? 1 : -1, xd = x.d, yd = y.d;\n        // Either 0?\n        if (!x.s) return new Ctor(x);\n        if (!y.s) throw Error(decimalError + \"Division by zero\");\n        e = x.e - y.e;\n        yL = yd.length;\n        xL = xd.length;\n        q = new Ctor(sign);\n        qd = q.d = [];\n        // Result exponent may be one less than e.\n        for(i = 0; yd[i] == (xd[i] || 0);)++i;\n        if (yd[i] > (xd[i] || 0)) --e;\n        if (pr == null) {\n            sd = pr = Ctor.precision;\n        } else if (dp) {\n            sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\n        } else {\n            sd = pr;\n        }\n        if (sd < 0) return new Ctor(0);\n        // Convert precision in number of base 10 digits to base 1e7 digits.\n        sd = sd / LOG_BASE + 2 | 0;\n        i = 0;\n        // divisor < 1e7\n        if (yL == 1) {\n            k = 0;\n            yd = yd[0];\n            sd++;\n            // k is the carry.\n            for(; (i < xL || k) && sd--; i++){\n                t = k * BASE + (xd[i] || 0);\n                qd[i] = t / yd | 0;\n                k = t % yd | 0;\n            }\n        // divisor >= 1e7\n        } else {\n            // Normalise xd and yd so highest order digit of yd is >= BASE/2\n            k = BASE / (yd[0] + 1) | 0;\n            if (k > 1) {\n                yd = multiplyInteger(yd, k);\n                xd = multiplyInteger(xd, k);\n                yL = yd.length;\n                xL = xd.length;\n            }\n            xi = yL;\n            rem = xd.slice(0, yL);\n            remL = rem.length;\n            // Add zeros to make remainder as long as divisor.\n            for(; remL < yL;)rem[remL++] = 0;\n            yz = yd.slice();\n            yz.unshift(0);\n            yd0 = yd[0];\n            if (yd[1] >= BASE / 2) ++yd0;\n            do {\n                k = 0;\n                // Compare divisor and remainder.\n                cmp = compare(yd, rem, yL, remL);\n                // If divisor < remainder.\n                if (cmp < 0) {\n                    // Calculate trial digit, k.\n                    rem0 = rem[0];\n                    if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\n                    // k will be how many times the divisor goes into the current remainder.\n                    k = rem0 / yd0 | 0;\n                    //  Algorithm:\n                    //  1. product = divisor * trial digit (k)\n                    //  2. if product > remainder: product -= divisor, k--\n                    //  3. remainder -= product\n                    //  4. if product was < remainder at 2:\n                    //    5. compare new remainder and divisor\n                    //    6. If remainder > divisor: remainder -= divisor, k++\n                    if (k > 1) {\n                        if (k >= BASE) k = BASE - 1;\n                        // product = divisor * trial digit.\n                        prod = multiplyInteger(yd, k);\n                        prodL = prod.length;\n                        remL = rem.length;\n                        // Compare product and remainder.\n                        cmp = compare(prod, rem, prodL, remL);\n                        // product > remainder.\n                        if (cmp == 1) {\n                            k--;\n                            // Subtract divisor from product.\n                            subtract(prod, yL < prodL ? yz : yd, prodL);\n                        }\n                    } else {\n                        // cmp is -1.\n                        // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n                        // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n                        if (k == 0) cmp = k = 1;\n                        prod = yd.slice();\n                    }\n                    prodL = prod.length;\n                    if (prodL < remL) prod.unshift(0);\n                    // Subtract product from remainder.\n                    subtract(rem, prod, remL);\n                    // If product was < previous remainder.\n                    if (cmp == -1) {\n                        remL = rem.length;\n                        // Compare divisor and new remainder.\n                        cmp = compare(yd, rem, yL, remL);\n                        // If divisor < new remainder, subtract divisor from remainder.\n                        if (cmp < 1) {\n                            k++;\n                            // Subtract divisor from remainder.\n                            subtract(rem, yL < remL ? yz : yd, remL);\n                        }\n                    }\n                    remL = rem.length;\n                } else if (cmp === 0) {\n                    k++;\n                    rem = [\n                        0\n                    ];\n                } // if cmp === 1, k will be 0\n                // Add the next digit, k, to the result array.\n                qd[i++] = k;\n                // Update the remainder.\n                if (cmp && rem[0]) {\n                    rem[remL++] = xd[xi] || 0;\n                } else {\n                    rem = [\n                        xd[xi]\n                    ];\n                    remL = 1;\n                }\n            }while ((xi++ < xL || rem[0] !== void 0) && sd--);\n        }\n        // Leading zero?\n        if (!qd[0]) qd.shift();\n        q.e = e;\n        return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\n    };\n}();\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n * significant digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n */ function exp(x, sd) {\n    var denominator, guard, pow, sum, t, wpr, i = 0, k = 0, Ctor = x.constructor, pr = Ctor.precision;\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\n    // exp(0) = 1\n    if (!x.s) return new Ctor(ONE);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    t = new Ctor(0.03125);\n    while(x.abs().gte(0.1)){\n        x = x.times(t); // x = x / 2^5\n        k += 5;\n    }\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n    wpr += guard;\n    denominator = pow = sum = new Ctor(ONE);\n    Ctor.precision = wpr;\n    for(;;){\n        pow = round(pow.times(x), wpr);\n        denominator = denominator.times(++i);\n        t = sum.plus(divide(pow, denominator, wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            while(k--)sum = round(sum.times(sum), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n    }\n}\n// Calculate the base 10 exponent from the base 1e7 exponent.\nfunction getBase10Exponent(x) {\n    var e = x.e * LOG_BASE, w = x.d[0];\n    // Add the number of digits of the first word of the digits array.\n    for(; w >= 10; w /= 10)e++;\n    return e;\n}\nfunction getLn10(Ctor, sd, pr) {\n    if (sd > Ctor.LN10.sd()) {\n        // Reset global state in case the exception is caught.\n        external = true;\n        if (pr) Ctor.precision = pr;\n        throw Error(decimalError + \"LN10 precision limit exceeded\");\n    }\n    return round(new Ctor(Ctor.LN10), sd);\n}\nfunction getZeroString(k) {\n    var zs = \"\";\n    for(; k--;)zs += \"0\";\n    return zs;\n}\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n * digits.\r\n *\r\n *  ln(n) is non-terminating (n != 1)\r\n *\r\n */ function ln(y, sd) {\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2, n = 1, guard = 10, x = y, xd = x.d, Ctor = x.constructor, pr = Ctor.precision;\n    // ln(-x) = NaN\n    // ln(0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // ln(1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    if (x.eq(10)) {\n        if (sd == null) external = true;\n        return getLn10(Ctor, wpr);\n    }\n    wpr += guard;\n    Ctor.precision = wpr;\n    c = digitsToString(xd);\n    c0 = c.charAt(0);\n    e = getBase10Exponent(x);\n    if (Math.abs(e) < 1.5e15) {\n        // Argument reduction.\n        // The series converges faster the closer the argument is to 1, so using\n        // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n        // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n        // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n        // later be divided by this number, then separate out the power of 10 using\n        // ln(a*10^b) = ln(a) + b*ln(10).\n        // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n        //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n        // max n is 6 (gives 0.7 - 1.3)\n        while(c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3){\n            x = x.times(y);\n            c = digitsToString(x.d);\n            c0 = c.charAt(0);\n            n++;\n        }\n        e = getBase10Exponent(x);\n        if (c0 > 1) {\n            x = new Ctor(\"0.\" + c);\n            e++;\n        } else {\n            x = new Ctor(c0 + \".\" + c.slice(1));\n        }\n    } else {\n        // The argument reduction method above may result in overflow if the argument y is a massive\n        // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n        // function using ln(x*10^e) = ln(x) + e*ln(10).\n        t = getLn10(Ctor, wpr + 2, pr).times(e + \"\");\n        x = ln(new Ctor(c0 + \".\" + c.slice(1)), wpr - guard).plus(t);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(x, pr)) : x;\n    }\n    // x is reduced to a value near 1.\n    // Taylor series.\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\n    x2 = round(x.times(x), wpr);\n    denominator = 3;\n    for(;;){\n        numerator = round(numerator.times(x2), wpr);\n        t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            sum = sum.times(2);\n            // Reverse the argument reduction.\n            if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + \"\"));\n            sum = divide(sum, new Ctor(n), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n        denominator += 2;\n    }\n}\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */ function parseDecimal(x, str) {\n    var e, i, len;\n    // Decimal point?\n    if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n    // Exponential form?\n    if ((i = str.search(/e/i)) > 0) {\n        // Determine exponent.\n        if (e < 0) e = i;\n        e += +str.slice(i + 1);\n        str = str.substring(0, i);\n    } else if (e < 0) {\n        // Integer.\n        e = str.length;\n    }\n    // Determine leading zeros.\n    for(i = 0; str.charCodeAt(i) === 48;)++i;\n    // Determine trailing zeros.\n    for(len = str.length; str.charCodeAt(len - 1) === 48;)--len;\n    str = str.slice(i, len);\n    if (str) {\n        len -= i;\n        e = e - i - 1;\n        x.e = mathfloor(e / LOG_BASE);\n        x.d = [];\n        // Transform base\n        // e is the base 10 exponent.\n        // i is where to slice str to get the first word of the digits array.\n        i = (e + 1) % LOG_BASE;\n        if (e < 0) i += LOG_BASE;\n        if (i < len) {\n            if (i) x.d.push(+str.slice(0, i));\n            for(len -= LOG_BASE; i < len;)x.d.push(+str.slice(i, i += LOG_BASE));\n            str = str.slice(i);\n            i = LOG_BASE - str.length;\n        } else {\n            i -= len;\n        }\n        for(; i--;)str += \"0\";\n        x.d.push(+str);\n        if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\n    } else {\n        // Zero.\n        x.s = 0;\n        x.e = 0;\n        x.d = [\n            0\n        ];\n    }\n    return x;\n}\n/*\r\n * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n */ function round(x, sd, rm) {\n    var i, j, k, n, rd, doRound, w, xdi, xd = x.d;\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // n: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n    // Get the length of the first word of the digits array xd.\n    for(n = 1, k = xd[0]; k >= 10; k /= 10)n++;\n    i = sd - n;\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n        i += LOG_BASE;\n        j = sd;\n        w = xd[xdi = 0];\n    } else {\n        xdi = Math.ceil((i + 1) / LOG_BASE);\n        k = xd.length;\n        if (xdi >= k) return x;\n        w = k = xd[xdi];\n        // Get the number of digits of w.\n        for(n = 1; k >= 10; k /= 10)n++;\n        // Get the index of rd within w.\n        i %= LOG_BASE;\n        // Get the index of rd within w, adjusted for leading zeros.\n        // The number of leading zeros of w is given by LOG_BASE - n.\n        j = i - LOG_BASE + n;\n    }\n    if (rm !== void 0) {\n        k = mathpow(10, n - j - 1);\n        // Get the rounding digit at index j of w.\n        rd = w / k % 10 | 0;\n        // Are there any non-zero digits after the rounding digit?\n        doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\n        // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\n        // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\n        // 714.\n        doRound = rm < 4 ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 && (i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    }\n    if (sd < 1 || !xd[0]) {\n        if (doRound) {\n            k = getBase10Exponent(x);\n            xd.length = 1;\n            // Convert sd to decimal places.\n            sd = sd - k - 1;\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n            xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n            x.e = mathfloor(-sd / LOG_BASE) || 0;\n        } else {\n            xd.length = 1;\n            // Zero.\n            xd[0] = x.e = x.s = 0;\n        }\n        return x;\n    }\n    // Remove excess digits.\n    if (i == 0) {\n        xd.length = xdi;\n        k = 1;\n        xdi--;\n    } else {\n        xd.length = xdi + 1;\n        k = mathpow(10, LOG_BASE - i);\n        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n        // j > 0 means i > number of leading zeros of w.\n        xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (doRound) {\n        for(;;){\n            // Is the digit to be rounded up in the first word of xd?\n            if (xdi == 0) {\n                if ((xd[0] += k) == BASE) {\n                    xd[0] = 1;\n                    ++x.e;\n                }\n                break;\n            } else {\n                xd[xdi] += k;\n                if (xd[xdi] != BASE) break;\n                xd[xdi--] = 0;\n                k = 1;\n            }\n        }\n    }\n    // Remove trailing zeros.\n    for(i = xd.length; xd[--i] === 0;)xd.pop();\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\n        throw Error(exponentOutOfRange + getBase10Exponent(x));\n    }\n    return x;\n}\nfunction subtract(x, y) {\n    var d, e, i, j, k, len, xd, xe, xLTy, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // Return y negated if x is zero.\n    // Return x if y is zero and x is non-zero.\n    if (!x.s || !y.s) {\n        if (y.s) y.s = -y.s;\n        else y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are non-zero numbers with the same sign.\n    e = y.e;\n    xe = x.e;\n    xd = xd.slice();\n    k = xe - e;\n    // If exponents differ...\n    if (k) {\n        xLTy = k < 0;\n        if (xLTy) {\n            d = xd;\n            k = -k;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = xe;\n            len = xd.length;\n        }\n        // Numbers with massively different exponents would result in a very high number of zeros\n        // needing to be prepended, but this can be avoided while still ensuring correct rounding by\n        // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n        i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n        if (k > i) {\n            k = i;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents.\n        d.reverse();\n        for(i = k; i--;)d.push(0);\n        d.reverse();\n    // Base 1e7 exponents equal.\n    } else {\n        // Check digits to determine which is the bigger number.\n        i = xd.length;\n        len = yd.length;\n        xLTy = i < len;\n        if (xLTy) len = i;\n        for(i = 0; i < len; i++){\n            if (xd[i] != yd[i]) {\n                xLTy = xd[i] < yd[i];\n                break;\n            }\n        }\n        k = 0;\n    }\n    if (xLTy) {\n        d = xd;\n        xd = yd;\n        yd = d;\n        y.s = -y.s;\n    }\n    len = xd.length;\n    // Append zeros to xd if shorter.\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\n    for(i = yd.length - len; i > 0; --i)xd[len++] = 0;\n    // Subtract yd from xd.\n    for(i = yd.length; i > k;){\n        if (xd[--i] < yd[i]) {\n            for(j = i; j && xd[--j] === 0;)xd[j] = BASE - 1;\n            --xd[j];\n            xd[i] += BASE;\n        }\n        xd[i] -= yd[i];\n    }\n    // Remove trailing zeros.\n    for(; xd[--len] === 0;)xd.pop();\n    // Remove leading zeros and adjust exponent accordingly.\n    for(; xd[0] === 0; xd.shift())--e;\n    // Zero?\n    if (!xd[0]) return new Ctor(0);\n    y.d = xd;\n    y.e = e;\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\n    return external ? round(y, pr) : y;\n}\nfunction toString(x, isExp, sd) {\n    var k, e = getBase10Exponent(x), str = digitsToString(x.d), len = str.length;\n    if (isExp) {\n        if (sd && (k = sd - len) > 0) {\n            str = str.charAt(0) + \".\" + str.slice(1) + getZeroString(k);\n        } else if (len > 1) {\n            str = str.charAt(0) + \".\" + str.slice(1);\n        }\n        str = str + (e < 0 ? \"e\" : \"e+\") + e;\n    } else if (e < 0) {\n        str = \"0.\" + getZeroString(-e - 1) + str;\n        if (sd && (k = sd - len) > 0) str += getZeroString(k);\n    } else if (e >= len) {\n        str += getZeroString(e + 1 - len);\n        if (sd && (k = sd - e - 1) > 0) str = str + \".\" + getZeroString(k);\n    } else {\n        if ((k = e + 1) < len) str = str.slice(0, k) + \".\" + str.slice(k);\n        if (sd && (k = sd - len) > 0) {\n            if (e + 1 === len) str += \".\";\n            str += getZeroString(k);\n        }\n    }\n    return x.s < 0 ? \"-\" + str : str;\n}\n// Does not strip trailing zeros.\nfunction truncate(arr, len) {\n    if (arr.length > len) {\n        arr.length = len;\n        return true;\n    }\n}\n// Decimal methods\n/*\r\n *  clone\r\n *  config/set\r\n */ /*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */ function clone(obj) {\n    var i, p, ps;\n    /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * value {number|string|Decimal} A numeric value.\r\n   *\r\n   */ function Decimal(value) {\n        var x = this;\n        // Decimal called without new.\n        if (!(x instanceof Decimal)) return new Decimal(value);\n        // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n        // which points to Object.\n        x.constructor = Decimal;\n        // Duplicate.\n        if (value instanceof Decimal) {\n            x.s = value.s;\n            x.e = value.e;\n            x.d = (value = value.d) ? value.slice() : value;\n            return;\n        }\n        if (typeof value === \"number\") {\n            // Reject Infinity/NaN.\n            if (value * 0 !== 0) {\n                throw Error(invalidArgument + value);\n            }\n            if (value > 0) {\n                x.s = 1;\n            } else if (value < 0) {\n                value = -value;\n                x.s = -1;\n            } else {\n                x.s = 0;\n                x.e = 0;\n                x.d = [\n                    0\n                ];\n                return;\n            }\n            // Fast path for small integers.\n            if (value === ~~value && value < 1e7) {\n                x.e = 0;\n                x.d = [\n                    value\n                ];\n                return;\n            }\n            return parseDecimal(x, value.toString());\n        } else if (typeof value !== \"string\") {\n            throw Error(invalidArgument + value);\n        }\n        // Minus sign?\n        if (value.charCodeAt(0) === 45) {\n            value = value.slice(1);\n            x.s = -1;\n        } else {\n            x.s = 1;\n        }\n        if (isDecimal.test(value)) parseDecimal(x, value);\n        else throw Error(invalidArgument + value);\n    }\n    Decimal.prototype = P;\n    Decimal.ROUND_UP = 0;\n    Decimal.ROUND_DOWN = 1;\n    Decimal.ROUND_CEIL = 2;\n    Decimal.ROUND_FLOOR = 3;\n    Decimal.ROUND_HALF_UP = 4;\n    Decimal.ROUND_HALF_DOWN = 5;\n    Decimal.ROUND_HALF_EVEN = 6;\n    Decimal.ROUND_HALF_CEIL = 7;\n    Decimal.ROUND_HALF_FLOOR = 8;\n    Decimal.clone = clone;\n    Decimal.config = Decimal.set = config;\n    if (obj === void 0) obj = {};\n    if (obj) {\n        ps = [\n            \"precision\",\n            \"rounding\",\n            \"toExpNeg\",\n            \"toExpPos\",\n            \"LN10\"\n        ];\n        for(i = 0; i < ps.length;)if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n    Decimal.config(obj);\n    return Decimal;\n}\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */ function config(obj) {\n    if (!obj || typeof obj !== \"object\") {\n        throw Error(decimalError + \"Object expected\");\n    }\n    var i, p, v, ps = [\n        \"precision\",\n        1,\n        MAX_DIGITS,\n        \"rounding\",\n        0,\n        8,\n        \"toExpNeg\",\n        -1 / 0,\n        0,\n        \"toExpPos\",\n        0,\n        1 / 0\n    ];\n    for(i = 0; i < ps.length; i += 3){\n        if ((v = obj[p = ps[i]]) !== void 0) {\n            if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\n            else throw Error(invalidArgument + p + \": \" + v);\n        }\n    }\n    if ((v = obj[p = \"LN10\"]) !== void 0) {\n        if (v == Math.LN10) this[p] = new this(v);\n        else throw Error(invalidArgument + p + \": \" + v);\n    }\n    return this;\n}\n// Create and configure initial Decimal constructor.\nvar Decimal = clone(defaults);\n// Internal constant.\nONE = new Decimal(1);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Decimal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/decimal.js-light/decimal.mjs\n");

/***/ })

};
;