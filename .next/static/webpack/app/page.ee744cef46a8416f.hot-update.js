"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/ThemeToggle */ \"(app-pages-browser)/./src/components/ui/ThemeToggle.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _features_NotificationCenter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./features/NotificationCenter */ \"(app-pages-browser)/./src/components/features/NotificationCenter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const today = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayKey)();\n    const formattedDate = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.formatDate)(today);\n    const checklist = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist)();\n    const notifications = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const { showMobileMenu, toggleMobileMenu, toggleModal } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const unreadNotifications = notifications.filter((n)=>!n.read).length;\n    const checkedCount = Object.values(checklist.items).filter(Boolean).length;\n    const isReadyToTrade = checkedCount >= 3;\n    const isSessionActive = checklist.isSessionActive;\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Calculate session duration\n    const getSessionDuration = ()=>{\n        if (!checklist.sessionStartTime) return null;\n        const start = new Date(checklist.sessionStartTime);\n        const now = new Date();\n        const diff = Math.floor((now - start) / 1000 / 60) // minutes\n        ;\n        const hours = Math.floor(diff / 60);\n        const minutes = diff % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(minutes, \"m\") : \"\".concat(minutes, \"m\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                className: \"bg-white/80 dark:bg-dark-900/80 backdrop-blur-lg border-b border-gray-200/20 dark:border-dark-700/20 sticky top-0 z-50\",\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"flex items-center space-x-3\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-10 h-10 lg:w-12 lg:h-12 rounded-xl overflow-hidden shadow-lg ring-2 ring-primary-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/LimitlessLogo.jpg\",\n                                            alt: \"Limitless Options Logo\",\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl lg:text-2xl font-bold bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent\",\n                                                children: \"Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs lg:text-sm text-gray-600 dark:text-gray-400 font-medium\",\n                                                children: \"Trading Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                    children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                        hour: \"2-digit\",\n                                                        minute: \"2-digit\",\n                                                        hour12: true\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: formattedDate.split(\",\")[0]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"\\n                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                animate: isReadyToTrade ? {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                } : {},\n                                                transition: {\n                                                    duration: 0.5,\n                                                    repeat: isReadyToTrade ? Infinity : 0,\n                                                    repeatDelay: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"flex items-center space-x-2 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getSessionDuration()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                            icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                            variant: \"ghost\",\n                                                            onClick: ()=>setShowNotifications(!showNotifications),\n                                                            className: \"relative\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                            initial: {\n                                                                scale: 0\n                                                            },\n                                                            animate: {\n                                                                scale: 1\n                                                            },\n                                                            transition: {\n                                                                type: \"spring\",\n                                                                stiffness: 500,\n                                                                damping: 30\n                                                            },\n                                                            children: unreadNotifications\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                            lineNumber: 154,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                variant: \"ghost\",\n                                                onClick: ()=>toggleModal(\"settings\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"scale-75\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        icon: showMobileMenu ? _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                        variant: \"ghost\",\n                                        onClick: toggleMobileMenu\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: toggleMobileMenu,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"absolute top-16 right-4 left-4 bg-white dark:bg-dark-800 rounded-2xl shadow-xl border border-gray-200 dark:border-dark-700 p-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\",\n                                                    hour12: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: formattedDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 218,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 210,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                    flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 224,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Session: \",\n                                                            getSessionDuration()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 236,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"notifications\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: [\n                                            \"Notifications\",\n                                            unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: unreadNotifications\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 246,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"stats\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Statistics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"settings\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 194,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_NotificationCenter__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                isOpen: showNotifications,\n                onClose: ()=>setShowNotifications(false)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"zNkunBbG0HAPpbu29KHOPKOKPz4=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.js\n"));

/***/ })

});