"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/search.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Search; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Search\", [\n  [\"circle\", { cx: \"11\", cy: \"11\", r: \"8\", key: \"4ej97u\" }],\n  [\"path\", { d: \"m21 21-4.3-4.3\", key: \"1qie3q\" }]\n]);\n\n\n//# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2VhcmNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsZUFBZSxnRUFBZ0I7QUFDL0IsZUFBZSwyQ0FBMkM7QUFDMUQsYUFBYSxvQ0FBb0M7QUFDakQ7O0FBRTZCO0FBQzdCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvc2VhcmNoLmpzP2Y2OWMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBTZWFyY2ggPSBjcmVhdGVMdWNpZGVJY29uKFwiU2VhcmNoXCIsIFtcbiAgW1wiY2lyY2xlXCIsIHsgY3g6IFwiMTFcIiwgY3k6IFwiMTFcIiwgcjogXCI4XCIsIGtleTogXCI0ZWo5N3VcIiB9XSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwibTIxIDIxLTQuMy00LjNcIiwga2V5OiBcIjFxaWUzcVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgU2VhcmNoIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXNlYXJjaC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/JournalEntry.js":
/*!****************************************!*\
  !*** ./src/components/JournalEntry.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JournalEntry; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _journal_RichTextEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./journal/RichTextEditor */ \"(app-pages-browser)/./src/components/journal/RichTextEditor.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction JournalEntry() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDatePicker, setShowDatePicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { journal, setJournalEntry, setSelectedDate, saveJournalEntry: saveToStore } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getAllJournalDates)();\n    const availableTags = [\n        \"Analysis\",\n        \"Emotions\",\n        \"Strategy\",\n        \"Lessons\",\n        \"Market\",\n        \"Performance\",\n        \"Goals\"\n    ];\n    // Load journal entry for selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEntry = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.loadJournalEntry)(journal.selectedDate);\n        if (savedEntry) {\n            setJournalEntry(savedEntry.content || \"\");\n            setSelectedTags(savedEntry.tags || []);\n            setLastSaved(savedEntry.lastUpdated);\n        } else {\n            setJournalEntry(\"\");\n            setSelectedTags([]);\n            setLastSaved(null);\n        }\n        setIsLoading(false);\n    }, [\n        journal.selectedDate\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && journal.currentEntry.trim()) {\n            const timeoutId = setTimeout(()=>{\n                handleSave(false) // Silent save\n                ;\n            }, 2000) // Auto-save after 2 seconds of inactivity\n            ;\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        journal.currentEntry,\n        isLoading\n    ]);\n    const handleSave = async function() {\n        let showFeedback = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        if (showFeedback) setIsSaving(true);\n        try {\n            const success = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.saveJournalEntry)(journal.selectedDate, journal.currentEntry);\n            saveToStore(journal.selectedDate, journal.currentEntry, selectedTags);\n            if (success) {\n                setLastSaved(new Date().toISOString());\n                if (showFeedback) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Journal entry saved successfully!\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Error saving journal entry:\", error);\n            if (showFeedback) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to save journal entry\");\n            }\n        }\n        if (showFeedback) {\n            setTimeout(()=>setIsSaving(false), 500);\n        }\n    };\n    const handleDateChange = (newDate)=>{\n        setSelectedDate(newDate);\n        setShowDatePicker(false);\n    };\n    const navigateDate = (direction)=>{\n        const currentIndex = journalDates.indexOf(journal.selectedDate);\n        if (direction === \"prev\" && currentIndex < journalDates.length - 1) {\n            setSelectedDate(journalDates[currentIndex + 1]);\n        } else if (direction === \"next\" && currentIndex > 0) {\n            setSelectedDate(journalDates[currentIndex - 1]);\n        }\n    };\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const getWordCount = ()=>{\n        const text = journal.currentEntry.replace(/<[^>]*>/g, \"\") // Strip HTML tags\n        ;\n        return text.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n    };\n    const getCharacterCount = ()=>{\n        return journal.currentEntry.length;\n    };\n    const exportEntry = ()=>{\n        const content = \"# Trading Journal Entry - \".concat((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(journal.selectedDate), \"\\n\\n\").concat(journal.currentEntry.replace(/<[^>]*>/g, \"\"));\n        const blob = new Blob([\n            content\n        ], {\n            type: \"text/markdown\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"journal-\".concat(journal.selectedDate, \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Journal entry exported!\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            variant: \"glass\",\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 dark:bg-dark-700 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-64 bg-gray-100 dark:bg-dark-800 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"glass\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Daily Trading Journal\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 text-sm mt-1\",\n                                                    children: \"Record your thoughts, analysis, and lessons learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            onClick: ()=>setShowSearch(!showSearch),\n                                            className: showSearch ? \"bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                            onClick: exportEntry,\n                                            disabled: !journal.currentEntry.trim(),\n                                            title: \"Export entry\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        journalDates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                    onClick: ()=>navigateDate(\"prev\"),\n                                                    disabled: journalDates.indexOf(journal.selectedDate) === journalDates.length - 1,\n                                                    title: \"Previous entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                    onClick: ()=>navigateDate(\"next\"),\n                                                    disabled: journalDates.indexOf(journal.selectedDate) === 0,\n                                                    title: \"Next entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"primary\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: ()=>handleSave(true),\n                                            disabled: isSaving || !journal.currentEntry.trim(),\n                                            loading: isSaving,\n                                            children: isSaving ? \"Saving...\" : \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                            children: showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    height: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    height: \"auto\",\n                                    opacity: 1\n                                },\n                                exit: {\n                                    height: 0,\n                                    opacity: 0\n                                },\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search journal entries...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-dark-700 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowDatePicker(!showDatePicker),\n                            className: \"flex items-center space-x-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-blue-700\",\n                                    children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(selectedDate)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 259,\n                            columnNumber: 11\n                        }, this),\n                        showDatePicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            className: \"absolute top-full left-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 z-10 min-w-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 mb-3\",\n                                        children: \"Select Date\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDateChange((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)()),\n                                        className: \"\\n                    w-full text-left px-3 py-2 rounded-lg transition-colors mb-2\\n                    \".concat(selectedDate === (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)() ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                  \"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 291,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600\",\n                                                children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)())\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 292,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 281,\n                                        columnNumber: 17\n                                    }, this),\n                                    journalDates.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-slate-600 mb-2 px-3\",\n                                                children: \"Previous Entries\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 298,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-48 overflow-y-auto\",\n                                                children: journalDates.map((date)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDateChange(date),\n                                                        className: \"\\n                            w-full text-left px-3 py-2 rounded-lg transition-colors mb-1\\n                            \".concat(selectedDate === date ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                          \"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(date)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                            lineNumber: 314,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, date, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 297,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 277,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: entry,\n                    onChange: (e)=>setEntry(e.target.value),\n                    placeholder: \"What happened in today's trading session? Record your analysis, emotions, lessons learned, and plans for tomorrow...\",\n                    className: \"w-full h-64 p-4 border-2 border-slate-200 rounded-xl resize-none focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-slate-800 placeholder-slate-400\",\n                    style: {\n                        minHeight: \"16rem\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 328,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 327,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-slate-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6 text-sm text-slate-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getWordCount(),\n                                    \" words\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getCharacterCount(),\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 342,\n                                columnNumber: 11\n                            }, this),\n                            lastSaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Saved \",\n                                            new Date(lastSaved).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            className: \"text-sm text-green-600 font-medium\",\n                            children: \"Auto-saving...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 354,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, this),\n            !entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-800 mb-3\",\n                        children: \"Journal Prompts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 372,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Market Analysis:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What was the overall market sentiment?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 377,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Which zones worked best today?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 378,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What patterns did you observe?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 376,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 374,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Personal Performance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• How did you feel during trades?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What mistakes did you make?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 386,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What will you improve tomorrow?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 387,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 384,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 382,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 373,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 367,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalEntry, \"GoQYXSsDyOsv7ERVEjpR2BxI4Ec=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\n_c = JournalEntry;\nvar _c;\n$RefreshReg$(_c, \"JournalEntry\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0pvdXJuYWxFbnRyeS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ0k7QUFvQmxDO0FBQzRGO0FBQzFEO0FBQ3ZCO0FBQ29DO0FBQ2Y7QUFDbEI7QUFFcEIsU0FBU3FDOztJQUN0QixNQUFNLENBQUNDLFdBQVdDLGFBQWEsR0FBR3ZDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ3dDLFVBQVVDLFlBQVksR0FBR3pDLCtDQUFRQSxDQUFDO0lBQ3pDLE1BQU0sQ0FBQzBDLFdBQVdDLGFBQWEsR0FBRzNDLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQzRDLGdCQUFnQkMsa0JBQWtCLEdBQUc3QywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUM4QyxjQUFjQyxnQkFBZ0IsR0FBRy9DLCtDQUFRQSxDQUFDLEVBQUU7SUFDbkQsTUFBTSxDQUFDZ0QsYUFBYUMsZUFBZSxHQUFHakQsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDa0QsWUFBWUMsY0FBYyxHQUFHbkQsK0NBQVFBLENBQUM7SUFFN0MsTUFBTSxFQUNKb0QsT0FBTyxFQUNQQyxlQUFlLEVBQ2ZDLGVBQWUsRUFDZi9CLGtCQUFrQmdDLFdBQVcsRUFDOUIsR0FBRzNCLHlEQUFRQTtJQUVaLE1BQU00QixlQUFlL0Isa0VBQWtCQTtJQUN2QyxNQUFNZ0MsZ0JBQWdCO1FBQUM7UUFBWTtRQUFZO1FBQVk7UUFBVztRQUFVO1FBQWU7S0FBUTtJQUV2Ryx1Q0FBdUM7SUFDdkN4RCxnREFBU0EsQ0FBQztRQUNSLE1BQU15RCxhQUFhbEMsZ0VBQWdCQSxDQUFDNEIsUUFBUU8sWUFBWTtRQUN4RCxJQUFJRCxZQUFZO1lBQ2RMLGdCQUFnQkssV0FBV0UsT0FBTyxJQUFJO1lBQ3RDYixnQkFBZ0JXLFdBQVdHLElBQUksSUFBSSxFQUFFO1lBQ3JDbEIsYUFBYWUsV0FBV0ksV0FBVztRQUNyQyxPQUFPO1lBQ0xULGdCQUFnQjtZQUNoQk4sZ0JBQWdCLEVBQUU7WUFDbEJKLGFBQWE7UUFDZjtRQUNBSixhQUFhO0lBQ2YsR0FBRztRQUFDYSxRQUFRTyxZQUFZO0tBQUM7SUFFekIsMEJBQTBCO0lBQzFCMUQsZ0RBQVNBLENBQUM7UUFDUixJQUFJLENBQUNxQyxhQUFhYyxRQUFRVyxZQUFZLENBQUNDLElBQUksSUFBSTtZQUM3QyxNQUFNQyxZQUFZQyxXQUFXO2dCQUMzQkMsV0FBVyxPQUFPLGNBQWM7O1lBQ2xDLEdBQUcsTUFBTSwwQ0FBMEM7O1lBRW5ELE9BQU8sSUFBTUMsYUFBYUg7UUFDNUI7SUFDRixHQUFHO1FBQUNiLFFBQVFXLFlBQVk7UUFBRXpCO0tBQVU7SUFFcEMsTUFBTTZCLGFBQWE7WUFBT0UsZ0ZBQWU7UUFDdkMsSUFBSUEsY0FBYzVCLFlBQVk7UUFFOUIsSUFBSTtZQUNGLE1BQU02QixVQUFVL0MsZ0VBQWdCQSxDQUFDNkIsUUFBUU8sWUFBWSxFQUFFUCxRQUFRVyxZQUFZO1lBQzNFUixZQUFZSCxRQUFRTyxZQUFZLEVBQUVQLFFBQVFXLFlBQVksRUFBRWpCO1lBRXhELElBQUl3QixTQUFTO2dCQUNYM0IsYUFBYSxJQUFJNEIsT0FBT0MsV0FBVztnQkFDbkMsSUFBSUgsY0FBYztvQkFDaEJqQyx1REFBS0EsQ0FBQ2tDLE9BQU8sQ0FBQztnQkFDaEI7WUFDRjtRQUNGLEVBQUUsT0FBT0csT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtZQUM3QyxJQUFJSixjQUFjO2dCQUNoQmpDLHVEQUFLQSxDQUFDcUMsS0FBSyxDQUFDO1lBQ2Q7UUFDRjtRQUVBLElBQUlKLGNBQWM7WUFDaEJILFdBQVcsSUFBTXpCLFlBQVksUUFBUTtRQUN2QztJQUNGO0lBRUEsTUFBTWtDLG1CQUFtQixDQUFDQztRQUN4QnRCLGdCQUFnQnNCO1FBQ2hCL0Isa0JBQWtCO0lBQ3BCO0lBRUEsTUFBTWdDLGVBQWUsQ0FBQ0M7UUFDcEIsTUFBTUMsZUFBZXZCLGFBQWF3QixPQUFPLENBQUM1QixRQUFRTyxZQUFZO1FBQzlELElBQUltQixjQUFjLFVBQVVDLGVBQWV2QixhQUFheUIsTUFBTSxHQUFHLEdBQUc7WUFDbEUzQixnQkFBZ0JFLFlBQVksQ0FBQ3VCLGVBQWUsRUFBRTtRQUNoRCxPQUFPLElBQUlELGNBQWMsVUFBVUMsZUFBZSxHQUFHO1lBQ25EekIsZ0JBQWdCRSxZQUFZLENBQUN1QixlQUFlLEVBQUU7UUFDaEQ7SUFDRjtJQUVBLE1BQU1HLGtCQUFrQixDQUFDQztRQUN2QnBDLGdCQUFnQnFDLENBQUFBLE9BQ2RBLEtBQUtDLFFBQVEsQ0FBQ0YsT0FDVkMsS0FBS0UsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxNQUFNSixPQUN2QjttQkFBSUM7Z0JBQU1EO2FBQUk7SUFFdEI7SUFFQSxNQUFNSyxlQUFlO1FBQ25CLE1BQU1DLE9BQU9yQyxRQUFRVyxZQUFZLENBQUMyQixPQUFPLENBQUMsWUFBWSxJQUFJLGtCQUFrQjs7UUFDNUUsT0FBT0QsS0FBS3pCLElBQUksR0FBRzJCLEtBQUssQ0FBQyxPQUFPTCxNQUFNLENBQUNNLENBQUFBLE9BQVFBLEtBQUtYLE1BQU0sR0FBRyxHQUFHQSxNQUFNO0lBQ3hFO0lBRUEsTUFBTVksb0JBQW9CO1FBQ3hCLE9BQU96QyxRQUFRVyxZQUFZLENBQUNrQixNQUFNO0lBQ3BDO0lBRUEsTUFBTWEsY0FBYztRQUNsQixNQUFNbEMsVUFBVSw2QkFBb0VSLE9BQXZDekIsMERBQVVBLENBQUN5QixRQUFRTyxZQUFZLEdBQUUsUUFBbUQsT0FBN0NQLFFBQVFXLFlBQVksQ0FBQzJCLE9BQU8sQ0FBQyxZQUFZO1FBQzdILE1BQU1LLE9BQU8sSUFBSUMsS0FBSztZQUFDcEM7U0FBUSxFQUFFO1lBQUVxQyxNQUFNO1FBQWdCO1FBQ3pELE1BQU1DLE1BQU1DLElBQUlDLGVBQWUsQ0FBQ0w7UUFDaEMsTUFBTU0sSUFBSUMsU0FBU0MsYUFBYSxDQUFDO1FBQ2pDRixFQUFFRyxJQUFJLEdBQUdOO1FBQ1RHLEVBQUVJLFFBQVEsR0FBRyxXQUFnQyxPQUFyQnJELFFBQVFPLFlBQVksRUFBQztRQUM3QzJDLFNBQVNJLElBQUksQ0FBQ0MsV0FBVyxDQUFDTjtRQUMxQkEsRUFBRU8sS0FBSztRQUNQTixTQUFTSSxJQUFJLENBQUNHLFdBQVcsQ0FBQ1I7UUFDMUJGLElBQUlXLGVBQWUsQ0FBQ1o7UUFDcEI5RCx1REFBS0EsQ0FBQ2tDLE9BQU8sQ0FBQztJQUNoQjtJQUVBLElBQUloQyxXQUFXO1FBQ2IscUJBQ0UsOERBQUNQLGdEQUFJQTtZQUFDZ0YsU0FBUTtZQUFRQyxXQUFVOzs4QkFDOUIsOERBQUNDO29CQUFJRCxXQUFVOzs7Ozs7OEJBQ2YsOERBQUNDO29CQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7SUFHckI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNqRixnREFBSUE7Z0JBQUNnRixTQUFROzBCQUNaLDRFQUFDL0UsZ0RBQVVBOztzQ0FDVCw4REFBQ2lGOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTtzREFDYiw0RUFBQ3pHLHVOQUFRQTtnREFBQ3lHLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ0M7OzhEQUNDLDhEQUFDaEYsK0NBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNpRjtvREFBRUYsV0FBVTs4REFBZ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FNakUsOERBQUNDO29DQUFJRCxXQUFVOztzREFFYiw4REFBQ2xGLGtEQUFNQTs0Q0FDTGlGLFNBQVE7NENBQ1JJLE1BQUs7NENBQ0xDLE1BQU0zRyx1TkFBTUE7NENBQ1o0RyxTQUFTLElBQU1sRSxjQUFjLENBQUNEOzRDQUM5QjhELFdBQVc5RCxhQUFhLGdGQUFnRjs7Ozs7O3NEQUkxRyw4REFBQ3BCLGtEQUFNQTs0Q0FDTGlGLFNBQVE7NENBQ1JJLE1BQUs7NENBQ0xDLE1BQU14Ryx3TkFBUUE7NENBQ2R5RyxTQUFTdkI7NENBQ1R3QixVQUFVLENBQUNsRSxRQUFRVyxZQUFZLENBQUNDLElBQUk7NENBQ3BDdUQsT0FBTTs7Ozs7O3dDQUlQL0QsYUFBYXlCLE1BQU0sR0FBRyxtQkFDckIsOERBQUNnQzs0Q0FBSUQsV0FBVTs7OERBQ2IsOERBQUNsRixrREFBTUE7b0RBQ0xpRixTQUFRO29EQUNSSSxNQUFLO29EQUNMQyxNQUFNMUcsd05BQVdBO29EQUNqQjJHLFNBQVMsSUFBTXhDLGFBQWE7b0RBQzVCeUMsVUFBVTlELGFBQWF3QixPQUFPLENBQUM1QixRQUFRTyxZQUFZLE1BQU1ILGFBQWF5QixNQUFNLEdBQUc7b0RBQy9Fc0MsT0FBTTs7Ozs7OzhEQUdSLDhEQUFDekYsa0RBQU1BO29EQUNMaUYsU0FBUTtvREFDUkksTUFBSztvREFDTEMsTUFBTXpHLHdOQUFZQTtvREFDbEIwRyxTQUFTLElBQU14QyxhQUFhO29EQUM1QnlDLFVBQVU5RCxhQUFhd0IsT0FBTyxDQUFDNUIsUUFBUU8sWUFBWSxNQUFNO29EQUN6RDRELE9BQU07Ozs7Ozs7Ozs7OztzREFNWiw4REFBQ3pGLGtEQUFNQTs0Q0FDTGlGLFNBQVE7NENBQ1JJLE1BQUs7NENBQ0xDLE1BQU0vRyx3TkFBSUE7NENBQ1ZnSCxTQUFTLElBQU1sRCxXQUFXOzRDQUMxQm1ELFVBQVU5RSxZQUFZLENBQUNZLFFBQVFXLFlBQVksQ0FBQ0MsSUFBSTs0Q0FDaER3RCxTQUFTaEY7c0RBRVJBLFdBQVcsY0FBYzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1oQyw4REFBQ3BDLDJEQUFlQTtzQ0FDYjhDLDRCQUNDLDhEQUFDL0Msa0RBQU1BLENBQUM4RyxHQUFHO2dDQUNUUSxTQUFTO29DQUFFQyxRQUFRO29DQUFHQyxTQUFTO2dDQUFFO2dDQUNqQ0MsU0FBUztvQ0FBRUYsUUFBUTtvQ0FBUUMsU0FBUztnQ0FBRTtnQ0FDdENFLE1BQU07b0NBQUVILFFBQVE7b0NBQUdDLFNBQVM7Z0NBQUU7Z0NBQzlCWCxXQUFVOzBDQUVWLDRFQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUN2Ryx1TkFBTUE7NENBQUN1RyxXQUFVOzs7Ozs7c0RBQ2xCLDhEQUFDYzs0Q0FDQzdCLE1BQUs7NENBQ0w4QixhQUFZOzRDQUNaQyxPQUFPaEY7NENBQ1BpRixVQUFVLENBQUNDLElBQU1qRixlQUFlaUYsRUFBRUMsTUFBTSxDQUFDSCxLQUFLOzRDQUM5Q2hCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFVeEIsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ2IsOERBQUNvQjs0QkFDQ2YsU0FBUyxJQUFNeEUsa0JBQWtCLENBQUNEOzRCQUNsQ29FLFdBQVU7OzhDQUVWLDhEQUFDMUcsd05BQVFBO29DQUFDMEcsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ3FCO29DQUFLckIsV0FBVTs4Q0FDYnJGLDBEQUFVQSxDQUFDZ0M7Ozs7Ozs7Ozs7Ozt3QkFLZmYsZ0NBQ0MsOERBQUN6QyxrREFBTUEsQ0FBQzhHLEdBQUc7NEJBQ1RRLFNBQVM7Z0NBQUVFLFNBQVM7Z0NBQUdXLEdBQUcsQ0FBQzs0QkFBRzs0QkFDOUJWLFNBQVM7Z0NBQUVELFNBQVM7Z0NBQUdXLEdBQUc7NEJBQUU7NEJBQzVCVCxNQUFNO2dDQUFFRixTQUFTO2dDQUFHVyxHQUFHLENBQUM7NEJBQUc7NEJBQzNCdEIsV0FBVTtzQ0FFViw0RUFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDdUI7d0NBQUd2QixXQUFVO2tEQUFvQzs7Ozs7O2tEQUdsRCw4REFBQ29CO3dDQUNDZixTQUFTLElBQU0xQyxpQkFBaUJqRCwyREFBV0E7d0NBQzNDc0YsV0FBVywyR0FLUixPQUhDckQsaUJBQWlCakMsMkRBQVdBLEtBQzFCLDhCQUNBLHFCQUNIOzswREFHSCw4REFBQ3VGO2dEQUFJRCxXQUFVOzBEQUFjOzs7Ozs7MERBQzdCLDhEQUFDQztnREFBSUQsV0FBVTswREFBMEJyRiwwREFBVUEsQ0FBQ0QsMkRBQVdBOzs7Ozs7Ozs7Ozs7b0NBSWhFOEIsYUFBYXlCLE1BQU0sR0FBRyxtQkFDckIsOERBQUNnQzs7MERBQ0MsOERBQUNBO2dEQUFJRCxXQUFVOzBEQUErQzs7Ozs7OzBEQUc5RCw4REFBQ0M7Z0RBQUlELFdBQVU7MERBQ1p4RCxhQUFhZ0YsR0FBRyxDQUFDQyxDQUFBQSxxQkFDaEIsOERBQUNMO3dEQUVDZixTQUFTLElBQU0xQyxpQkFBaUI4RDt3REFDaEN6QixXQUFXLDJIQUtSLE9BSENyRCxpQkFBaUI4RSxPQUNmLDhCQUNBLHFCQUNIO2tFQUdILDRFQUFDeEI7NERBQUlELFdBQVU7c0VBQVdyRiwwREFBVUEsQ0FBQzhHOzs7Ozs7dURBVmhDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBdUJ6Qiw4REFBQ3hCO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDMEI7b0JBQ0NDLEtBQUtDO29CQUNMWixPQUFPYTtvQkFDUFosVUFBVSxDQUFDQyxJQUFNWSxTQUFTWixFQUFFQyxNQUFNLENBQUNILEtBQUs7b0JBQ3hDRCxhQUFZO29CQUNaZixXQUFVO29CQUNWK0IsT0FBTzt3QkFBRUMsV0FBVztvQkFBUTs7Ozs7Ozs7Ozs7MEJBS2hDLDhEQUFDL0I7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNxQjs7b0NBQU03QztvQ0FBZTs7Ozs7OzswQ0FDdEIsOERBQUM2Qzs7b0NBQU14QztvQ0FBb0I7Ozs7Ozs7NEJBQzFCbkQsMkJBQ0MsOERBQUN1RTtnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUN4Ryx3TkFBS0E7d0NBQUN3RyxXQUFVOzs7Ozs7a0RBQ2pCLDhEQUFDcUI7OzRDQUFLOzRDQUFPLElBQUk5RCxLQUFLN0IsV0FBV3VHLGtCQUFrQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FNekQsOERBQUNoQzt3QkFBSUQsV0FBVTtrQ0FDWjZCLE1BQU03RSxJQUFJLG9CQUNULDhEQUFDN0Qsa0RBQU1BLENBQUM4RyxHQUFHOzRCQUNUUSxTQUFTO2dDQUFFRSxTQUFTO2dDQUFHdUIsT0FBTzs0QkFBSTs0QkFDbEN0QixTQUFTO2dDQUFFRCxTQUFTO2dDQUFHdUIsT0FBTzs0QkFBRTs0QkFDaENsQyxXQUFVO3NDQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztZQVFOLENBQUM2QixNQUFNN0UsSUFBSSxvQkFDViw4REFBQzdELGtEQUFNQSxDQUFDOEcsR0FBRztnQkFDVFEsU0FBUztvQkFBRUUsU0FBUztvQkFBR1csR0FBRztnQkFBRztnQkFDN0JWLFNBQVM7b0JBQUVELFNBQVM7b0JBQUdXLEdBQUc7Z0JBQUU7Z0JBQzVCdEIsV0FBVTs7a0NBRVYsOERBQUN1Qjt3QkFBR3ZCLFdBQVU7a0NBQW1DOzs7Ozs7a0NBQ2pELDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDOztrREFDQyw4REFBQ2tDO2tEQUFPOzs7Ozs7a0RBQ1IsOERBQUNDO3dDQUFHcEMsV0FBVTs7MERBQ1osOERBQUNxQzswREFBRzs7Ozs7OzBEQUNKLDhEQUFDQTswREFBRzs7Ozs7OzBEQUNKLDhEQUFDQTswREFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUdSLDhEQUFDcEM7O2tEQUNDLDhEQUFDa0M7a0RBQU87Ozs7OztrREFDUiw4REFBQ0M7d0NBQUdwQyxXQUFVOzswREFDWiw4REFBQ3FDOzBEQUFHOzs7Ozs7MERBQ0osOERBQUNBOzBEQUFHOzs7Ozs7MERBQ0osOERBQUNBOzBEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRcEI7R0EzV3dCaEg7O1FBY2xCVCxxREFBUUE7OztLQWRVUyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvY29tcG9uZW50cy9Kb3VybmFsRW50cnkuanM/ZDc2YiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBtb3Rpb24sIEFuaW1hdGVQcmVzZW5jZSB9IGZyb20gJ2ZyYW1lci1tb3Rpb24nXG5pbXBvcnQge1xuICBTYXZlLFxuICBDYWxlbmRhcixcbiAgQm9va09wZW4sXG4gIENsb2NrLFxuICBTZWFyY2gsXG4gIENoZXZyb25MZWZ0LFxuICBDaGV2cm9uUmlnaHQsXG4gIERvd25sb2FkLFxuICBVcGxvYWQsXG4gIFRhZyxcbiAgRmlsdGVyLFxuICBUcmVuZGluZ1VwLFxuICBCYXJDaGFydDMsXG4gIEZpbGVUZXh0LFxuICBTdGFyLFxuICBIZWFydCxcbiAgVGFyZ2V0LFxuICBaYXBcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuaW1wb3J0IHsgc2F2ZUpvdXJuYWxFbnRyeSwgbG9hZEpvdXJuYWxFbnRyeSwgZ2V0QWxsSm91cm5hbERhdGVzLCBnZXRUb2RheUtleSwgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvdXRpbHMvc3RvcmFnZSdcbmltcG9ydCB7IHVzZVN0b3JlLCB1c2VKb3VybmFsIH0gZnJvbSAnQC9zdG9yZS91c2VTdG9yZSdcbmltcG9ydCBCdXR0b24gZnJvbSAnLi91aS9CdXR0b24nXG5pbXBvcnQgQ2FyZCwgeyBDYXJkSGVhZGVyLCBDYXJkVGl0bGUsIENhcmRDb250ZW50IH0gZnJvbSAnLi91aS9DYXJkJ1xuaW1wb3J0IFJpY2hUZXh0RWRpdG9yIGZyb20gJy4vam91cm5hbC9SaWNoVGV4dEVkaXRvcidcbmltcG9ydCB0b2FzdCBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEpvdXJuYWxFbnRyeSgpIHtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG4gIGNvbnN0IFtpc1NhdmluZywgc2V0SXNTYXZpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtsYXN0U2F2ZWQsIHNldExhc3RTYXZlZF0gPSB1c2VTdGF0ZShudWxsKVxuICBjb25zdCBbc2hvd0RhdGVQaWNrZXIsIHNldFNob3dEYXRlUGlja2VyXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbc2VsZWN0ZWRUYWdzLCBzZXRTZWxlY3RlZFRhZ3NdID0gdXNlU3RhdGUoW10pXG4gIGNvbnN0IFtzZWFyY2hRdWVyeSwgc2V0U2VhcmNoUXVlcnldID0gdXNlU3RhdGUoJycpXG4gIGNvbnN0IFtzaG93U2VhcmNoLCBzZXRTaG93U2VhcmNoXSA9IHVzZVN0YXRlKGZhbHNlKVxuXG4gIGNvbnN0IHtcbiAgICBqb3VybmFsLFxuICAgIHNldEpvdXJuYWxFbnRyeSxcbiAgICBzZXRTZWxlY3RlZERhdGUsXG4gICAgc2F2ZUpvdXJuYWxFbnRyeTogc2F2ZVRvU3RvcmVcbiAgfSA9IHVzZVN0b3JlKClcblxuICBjb25zdCBqb3VybmFsRGF0ZXMgPSBnZXRBbGxKb3VybmFsRGF0ZXMoKVxuICBjb25zdCBhdmFpbGFibGVUYWdzID0gWydBbmFseXNpcycsICdFbW90aW9ucycsICdTdHJhdGVneScsICdMZXNzb25zJywgJ01hcmtldCcsICdQZXJmb3JtYW5jZScsICdHb2FscyddXG5cbiAgLy8gTG9hZCBqb3VybmFsIGVudHJ5IGZvciBzZWxlY3RlZCBkYXRlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRFbnRyeSA9IGxvYWRKb3VybmFsRW50cnkoam91cm5hbC5zZWxlY3RlZERhdGUpXG4gICAgaWYgKHNhdmVkRW50cnkpIHtcbiAgICAgIHNldEpvdXJuYWxFbnRyeShzYXZlZEVudHJ5LmNvbnRlbnQgfHwgJycpXG4gICAgICBzZXRTZWxlY3RlZFRhZ3Moc2F2ZWRFbnRyeS50YWdzIHx8IFtdKVxuICAgICAgc2V0TGFzdFNhdmVkKHNhdmVkRW50cnkubGFzdFVwZGF0ZWQpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNldEpvdXJuYWxFbnRyeSgnJylcbiAgICAgIHNldFNlbGVjdGVkVGFncyhbXSlcbiAgICAgIHNldExhc3RTYXZlZChudWxsKVxuICAgIH1cbiAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gIH0sIFtqb3VybmFsLnNlbGVjdGVkRGF0ZV0pXG5cbiAgLy8gQXV0by1zYXZlIGZ1bmN0aW9uYWxpdHlcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzTG9hZGluZyAmJiBqb3VybmFsLmN1cnJlbnRFbnRyeS50cmltKCkpIHtcbiAgICAgIGNvbnN0IHRpbWVvdXRJZCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICBoYW5kbGVTYXZlKGZhbHNlKSAvLyBTaWxlbnQgc2F2ZVxuICAgICAgfSwgMjAwMCkgLy8gQXV0by1zYXZlIGFmdGVyIDIgc2Vjb25kcyBvZiBpbmFjdGl2aXR5XG5cbiAgICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKVxuICAgIH1cbiAgfSwgW2pvdXJuYWwuY3VycmVudEVudHJ5LCBpc0xvYWRpbmddKVxuXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSBhc3luYyAoc2hvd0ZlZWRiYWNrID0gdHJ1ZSkgPT4ge1xuICAgIGlmIChzaG93RmVlZGJhY2spIHNldElzU2F2aW5nKHRydWUpXG5cbiAgICB0cnkge1xuICAgICAgY29uc3Qgc3VjY2VzcyA9IHNhdmVKb3VybmFsRW50cnkoam91cm5hbC5zZWxlY3RlZERhdGUsIGpvdXJuYWwuY3VycmVudEVudHJ5KVxuICAgICAgc2F2ZVRvU3RvcmUoam91cm5hbC5zZWxlY3RlZERhdGUsIGpvdXJuYWwuY3VycmVudEVudHJ5LCBzZWxlY3RlZFRhZ3MpXG5cbiAgICAgIGlmIChzdWNjZXNzKSB7XG4gICAgICAgIHNldExhc3RTYXZlZChuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkpXG4gICAgICAgIGlmIChzaG93RmVlZGJhY2spIHtcbiAgICAgICAgICB0b2FzdC5zdWNjZXNzKCdKb3VybmFsIGVudHJ5IHNhdmVkIHN1Y2Nlc3NmdWxseSEnKVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHNhdmluZyBqb3VybmFsIGVudHJ5OicsIGVycm9yKVxuICAgICAgaWYgKHNob3dGZWVkYmFjaykge1xuICAgICAgICB0b2FzdC5lcnJvcignRmFpbGVkIHRvIHNhdmUgam91cm5hbCBlbnRyeScpXG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKHNob3dGZWVkYmFjaykge1xuICAgICAgc2V0VGltZW91dCgoKSA9PiBzZXRJc1NhdmluZyhmYWxzZSksIDUwMClcbiAgICB9XG4gIH1cblxuICBjb25zdCBoYW5kbGVEYXRlQ2hhbmdlID0gKG5ld0RhdGUpID0+IHtcbiAgICBzZXRTZWxlY3RlZERhdGUobmV3RGF0ZSlcbiAgICBzZXRTaG93RGF0ZVBpY2tlcihmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IG5hdmlnYXRlRGF0ZSA9IChkaXJlY3Rpb24pID0+IHtcbiAgICBjb25zdCBjdXJyZW50SW5kZXggPSBqb3VybmFsRGF0ZXMuaW5kZXhPZihqb3VybmFsLnNlbGVjdGVkRGF0ZSlcbiAgICBpZiAoZGlyZWN0aW9uID09PSAncHJldicgJiYgY3VycmVudEluZGV4IDwgam91cm5hbERhdGVzLmxlbmd0aCAtIDEpIHtcbiAgICAgIHNldFNlbGVjdGVkRGF0ZShqb3VybmFsRGF0ZXNbY3VycmVudEluZGV4ICsgMV0pXG4gICAgfSBlbHNlIGlmIChkaXJlY3Rpb24gPT09ICduZXh0JyAmJiBjdXJyZW50SW5kZXggPiAwKSB7XG4gICAgICBzZXRTZWxlY3RlZERhdGUoam91cm5hbERhdGVzW2N1cnJlbnRJbmRleCAtIDFdKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVRhZ1RvZ2dsZSA9ICh0YWcpID0+IHtcbiAgICBzZXRTZWxlY3RlZFRhZ3MocHJldiA9PlxuICAgICAgcHJldi5pbmNsdWRlcyh0YWcpXG4gICAgICAgID8gcHJldi5maWx0ZXIodCA9PiB0ICE9PSB0YWcpXG4gICAgICAgIDogWy4uLnByZXYsIHRhZ11cbiAgICApXG4gIH1cblxuICBjb25zdCBnZXRXb3JkQ291bnQgPSAoKSA9PiB7XG4gICAgY29uc3QgdGV4dCA9IGpvdXJuYWwuY3VycmVudEVudHJ5LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpIC8vIFN0cmlwIEhUTUwgdGFnc1xuICAgIHJldHVybiB0ZXh0LnRyaW0oKS5zcGxpdCgvXFxzKy8pLmZpbHRlcih3b3JkID0+IHdvcmQubGVuZ3RoID4gMCkubGVuZ3RoXG4gIH1cblxuICBjb25zdCBnZXRDaGFyYWN0ZXJDb3VudCA9ICgpID0+IHtcbiAgICByZXR1cm4gam91cm5hbC5jdXJyZW50RW50cnkubGVuZ3RoXG4gIH1cblxuICBjb25zdCBleHBvcnRFbnRyeSA9ICgpID0+IHtcbiAgICBjb25zdCBjb250ZW50ID0gYCMgVHJhZGluZyBKb3VybmFsIEVudHJ5IC0gJHtmb3JtYXREYXRlKGpvdXJuYWwuc2VsZWN0ZWREYXRlKX1cXG5cXG4ke2pvdXJuYWwuY3VycmVudEVudHJ5LnJlcGxhY2UoLzxbXj5dKj4vZywgJycpfWBcbiAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L21hcmtkb3duJyB9KVxuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYilcbiAgICBjb25zdCBhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpXG4gICAgYS5ocmVmID0gdXJsXG4gICAgYS5kb3dubG9hZCA9IGBqb3VybmFsLSR7am91cm5hbC5zZWxlY3RlZERhdGV9Lm1kYFxuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQoYSlcbiAgICBhLmNsaWNrKClcbiAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKGEpXG4gICAgVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpXG4gICAgdG9hc3Quc3VjY2VzcygnSm91cm5hbCBlbnRyeSBleHBvcnRlZCEnKVxuICB9XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8Q2FyZCB2YXJpYW50PVwiZ2xhc3NcIiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC04IGJnLWdyYXktMjAwIGRhcms6YmctZGFyay03MDAgcm91bmRlZCBtYi00XCI+PC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC02NCBiZy1ncmF5LTEwMCBkYXJrOmJnLWRhcmstODAwIHJvdW5kZWQtbGdcIj48L2Rpdj5cbiAgICAgIDwvQ2FyZD5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICB7LyogSGVhZGVyIENhcmQgKi99XG4gICAgICA8Q2FyZCB2YXJpYW50PVwiZ2xhc3NcIj5cbiAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1hY2NlbnQtNTAwIHRvLWFjY2VudC02MDAgcm91bmRlZC14bCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPkRhaWx5IFRyYWRpbmcgSm91cm5hbDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgZGFyazp0ZXh0LWdyYXktNDAwIHRleHQtc20gbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgUmVjb3JkIHlvdXIgdGhvdWdodHMsIGFuYWx5c2lzLCBhbmQgbGVzc29ucyBsZWFybmVkXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICB7LyogU2VhcmNoIFRvZ2dsZSAqL31cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBpY29uPXtTZWFyY2h9XG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd1NlYXJjaCghc2hvd1NlYXJjaCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtzaG93U2VhcmNoID8gJ2JnLXByaW1hcnktNTAgZGFyazpiZy1wcmltYXJ5LTkwMC8yMCB0ZXh0LXByaW1hcnktNjAwIGRhcms6dGV4dC1wcmltYXJ5LTQwMCcgOiAnJ31cbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICB7LyogRXhwb3J0IEJ1dHRvbiAqL31cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICBpY29uPXtEb3dubG9hZH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtleHBvcnRFbnRyeX1cbiAgICAgICAgICAgICAgICBkaXNhYmxlZD17IWpvdXJuYWwuY3VycmVudEVudHJ5LnRyaW0oKX1cbiAgICAgICAgICAgICAgICB0aXRsZT1cIkV4cG9ydCBlbnRyeVwiXG4gICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgey8qIERhdGUgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICAgICAge2pvdXJuYWxEYXRlcy5sZW5ndGggPiAxICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxuICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICBpY29uPXtDaGV2cm9uTGVmdH1cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gbmF2aWdhdGVEYXRlKCdwcmV2Jyl9XG4gICAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtqb3VybmFsRGF0ZXMuaW5kZXhPZihqb3VybmFsLnNlbGVjdGVkRGF0ZSkgPT09IGpvdXJuYWxEYXRlcy5sZW5ndGggLSAxfVxuICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIlByZXZpb3VzIGVudHJ5XCJcbiAgICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgaWNvbj17Q2hldnJvblJpZ2h0fVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBuYXZpZ2F0ZURhdGUoJ25leHQnKX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2pvdXJuYWxEYXRlcy5pbmRleE9mKGpvdXJuYWwuc2VsZWN0ZWREYXRlKSA9PT0gMH1cbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCJOZXh0IGVudHJ5XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgey8qIFNhdmUgQnV0dG9uICovfVxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInByaW1hcnlcIlxuICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgaWNvbj17U2F2ZX1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVTYXZlKHRydWUpfVxuICAgICAgICAgICAgICAgIGRpc2FibGVkPXtpc1NhdmluZyB8fCAham91cm5hbC5jdXJyZW50RW50cnkudHJpbSgpfVxuICAgICAgICAgICAgICAgIGxvYWRpbmc9e2lzU2F2aW5nfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2lzU2F2aW5nID8gJ1NhdmluZy4uLicgOiAnU2F2ZSd9XG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogU2VhcmNoIEJhciAqL31cbiAgICAgICAgICA8QW5pbWF0ZVByZXNlbmNlPlxuICAgICAgICAgICAge3Nob3dTZWFyY2ggJiYgKFxuICAgICAgICAgICAgICA8bW90aW9uLmRpdlxuICAgICAgICAgICAgICAgIGluaXRpYWw9e3sgaGVpZ2h0OiAwLCBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgICAgYW5pbWF0ZT17eyBoZWlnaHQ6ICdhdXRvJywgb3BhY2l0eTogMSB9fVxuICAgICAgICAgICAgICAgIGV4aXQ9e3sgaGVpZ2h0OiAwLCBvcGFjaXR5OiAwIH19XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXQtNFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XG4gICAgICAgICAgICAgICAgICA8U2VhcmNoIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yIHctNCBoLTQgdGV4dC1ncmF5LTQwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlNlYXJjaCBqb3VybmFsIGVudHJpZXMuLi5cIlxuICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VhcmNoUXVlcnl9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U2VhcmNoUXVlcnkoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcGwtMTAgcHItNCBweS0yIGJvcmRlciBib3JkZXItZ3JheS0yMDAgZGFyazpib3JkZXItZGFyay03MDAgcm91bmRlZC1sZyBiZy13aGl0ZSBkYXJrOmJnLWRhcmstODAwIHRleHQtZ3JheS05MDAgZGFyazp0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNTAwIGRhcms6cGxhY2Vob2xkZXItZ3JheS00MDAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHJpbWFyeS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9BbmltYXRlUHJlc2VuY2U+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgIDwvQ2FyZD5cblxuICAgICAgey8qIERhdGUgU2VsZWN0b3IgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dEYXRlUGlja2VyKCFzaG93RGF0ZVBpY2tlcil9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgcHgtNCBweS0yIGJnLWJsdWUtNTAgaG92ZXI6YmctYmx1ZS0xMDAgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPENhbGVuZGFyIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTYwMFwiIC8+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgICAge2Zvcm1hdERhdGUoc2VsZWN0ZWREYXRlKX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgIHsvKiBEYXRlIFBpY2tlciBEcm9wZG93biAqL31cbiAgICAgICAgICB7c2hvd0RhdGVQaWNrZXIgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCB5OiAtMTAgfX1cbiAgICAgICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgICAgIGV4aXQ9e3sgb3BhY2l0eTogMCwgeTogLTEwIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC1mdWxsIGxlZnQtMCBtdC0yIGJnLXdoaXRlIHJvdW5kZWQteGwgc2hhZG93LXhsIGJvcmRlciBib3JkZXItc2xhdGUtMjAwIHotMTAgbWluLXctNjRcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtc2xhdGUtODAwIG1iLTNcIj5TZWxlY3QgRGF0ZTwvaDM+XG4gICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgey8qIFRvZGF5IE9wdGlvbiAqL31cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEYXRlQ2hhbmdlKGdldFRvZGF5S2V5KCkpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgXG4gICAgICAgICAgICAgICAgICAgIHctZnVsbCB0ZXh0LWxlZnQgcHgtMyBweS0yIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgbWItMlxuICAgICAgICAgICAgICAgICAgICAke3NlbGVjdGVkRGF0ZSA9PT0gZ2V0VG9kYXlLZXkoKVxuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnaG92ZXI6Ymctc2xhdGUtNTAnXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgIGB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPlRvZGF5PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1zbGF0ZS02MDBcIj57Zm9ybWF0RGF0ZShnZXRUb2RheUtleSgpKX08L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgICAgIHsvKiBQcmV2aW91cyBFbnRyaWVzICovfVxuICAgICAgICAgICAgICAgIHtqb3VybmFsRGF0ZXMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS02MDAgbWItMiBweC0zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgUHJldmlvdXMgRW50cmllc1xuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtaC00OCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgICB7am91cm5hbERhdGVzLm1hcChkYXRlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtkYXRlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBoYW5kbGVEYXRlQ2hhbmdlKGRhdGUpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB3LWZ1bGwgdGV4dC1sZWZ0IHB4LTMgcHktMiByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzIG1iLTFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAke3NlbGVjdGVkRGF0ZSA9PT0gZGF0ZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnYmctYmx1ZS0xMDAgdGV4dC1ibHVlLTcwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogJ2hvdmVyOmJnLXNsYXRlLTUwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgYH1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e2Zvcm1hdERhdGUoZGF0ZSl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvbW90aW9uLmRpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogSm91cm5hbCBUZXh0IEFyZWEgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTRcIj5cbiAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgcmVmPXt0ZXh0YXJlYVJlZn1cbiAgICAgICAgICB2YWx1ZT17ZW50cnl9XG4gICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFbnRyeShlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgcGxhY2Vob2xkZXI9XCJXaGF0IGhhcHBlbmVkIGluIHRvZGF5J3MgdHJhZGluZyBzZXNzaW9uPyBSZWNvcmQgeW91ciBhbmFseXNpcywgZW1vdGlvbnMsIGxlc3NvbnMgbGVhcm5lZCwgYW5kIHBsYW5zIGZvciB0b21vcnJvdy4uLlwiXG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtNjQgcC00IGJvcmRlci0yIGJvcmRlci1zbGF0ZS0yMDAgcm91bmRlZC14bCByZXNpemUtbm9uZSBmb2N1czpib3JkZXItYmx1ZS00MDAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS0yMDAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIHRleHQtc2xhdGUtODAwIHBsYWNlaG9sZGVyLXNsYXRlLTQwMFwiXG4gICAgICAgICAgc3R5bGU9e3sgbWluSGVpZ2h0OiAnMTZyZW0nIH19XG4gICAgICAgIC8+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIFN0YXRzIGFuZCBGb290ZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBwdC00IGJvcmRlci10IGJvcmRlci1zbGF0ZS0yMDBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTYgdGV4dC1zbSB0ZXh0LXNsYXRlLTYwMFwiPlxuICAgICAgICAgIDxzcGFuPntnZXRXb3JkQ291bnQoKX0gd29yZHM8L3NwYW4+XG4gICAgICAgICAgPHNwYW4+e2dldENoYXJhY3RlckNvdW50KCl9IGNoYXJhY3RlcnM8L3NwYW4+XG4gICAgICAgICAge2xhc3RTYXZlZCAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPlNhdmVkIHtuZXcgRGF0ZShsYXN0U2F2ZWQpLnRvTG9jYWxlVGltZVN0cmluZygpfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBRdWljayBBY3Rpb25zICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgIHtlbnRyeS50cmltKCkgJiYgKFxuICAgICAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICAgICAgaW5pdGlhbD17eyBvcGFjaXR5OiAwLCBzY2FsZTogMC44IH19XG4gICAgICAgICAgICAgIGFuaW1hdGU9e3sgb3BhY2l0eTogMSwgc2NhbGU6IDEgfX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTYwMCBmb250LW1lZGl1bVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEF1dG8tc2F2aW5nLi4uXG4gICAgICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEpvdXJuYWwgUHJvbXB0cyAqL31cbiAgICAgIHshZW50cnkudHJpbSgpICYmIChcbiAgICAgICAgPG1vdGlvbi5kaXZcbiAgICAgICAgICBpbml0aWFsPXt7IG9wYWNpdHk6IDAsIHk6IDIwIH19XG4gICAgICAgICAgYW5pbWF0ZT17eyBvcGFjaXR5OiAxLCB5OiAwIH19XG4gICAgICAgICAgY2xhc3NOYW1lPVwibXQtNiBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTUwIHJvdW5kZWQteGwgYm9yZGVyIGJvcmRlci1ibHVlLTIwMFwiXG4gICAgICAgID5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtODAwIG1iLTNcIj5Kb3VybmFsIFByb21wdHM8L2gzPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtMyB0ZXh0LXNtIHRleHQtYmx1ZS03MDBcIj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxzdHJvbmc+TWFya2V0IEFuYWx5c2lzOjwvc3Ryb25nPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibXQtMSBzcGFjZS15LTEgdGV4dC1ibHVlLTYwMFwiPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgV2hhdCB3YXMgdGhlIG92ZXJhbGwgbWFya2V0IHNlbnRpbWVudD88L2xpPlxuICAgICAgICAgICAgICAgIDxsaT7igKIgV2hpY2ggem9uZXMgd29ya2VkIGJlc3QgdG9kYXk/PC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4oCiIFdoYXQgcGF0dGVybnMgZGlkIHlvdSBvYnNlcnZlPzwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxzdHJvbmc+UGVyc29uYWwgUGVyZm9ybWFuY2U6PC9zdHJvbmc+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJtdC0xIHNwYWNlLXktMSB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiBIb3cgZGlkIHlvdSBmZWVsIGR1cmluZyB0cmFkZXM/PC9saT5cbiAgICAgICAgICAgICAgICA8bGk+4oCiIFdoYXQgbWlzdGFrZXMgZGlkIHlvdSBtYWtlPzwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPuKAoiBXaGF0IHdpbGwgeW91IGltcHJvdmUgdG9tb3Jyb3c/PC9saT5cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L21vdGlvbi5kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJtb3Rpb24iLCJBbmltYXRlUHJlc2VuY2UiLCJTYXZlIiwiQ2FsZW5kYXIiLCJCb29rT3BlbiIsIkNsb2NrIiwiU2VhcmNoIiwiQ2hldnJvbkxlZnQiLCJDaGV2cm9uUmlnaHQiLCJEb3dubG9hZCIsIlVwbG9hZCIsIlRhZyIsIkZpbHRlciIsIlRyZW5kaW5nVXAiLCJCYXJDaGFydDMiLCJGaWxlVGV4dCIsIlN0YXIiLCJIZWFydCIsIlRhcmdldCIsIlphcCIsInNhdmVKb3VybmFsRW50cnkiLCJsb2FkSm91cm5hbEVudHJ5IiwiZ2V0QWxsSm91cm5hbERhdGVzIiwiZ2V0VG9kYXlLZXkiLCJmb3JtYXREYXRlIiwidXNlU3RvcmUiLCJ1c2VKb3VybmFsIiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDYXJkQ29udGVudCIsIlJpY2hUZXh0RWRpdG9yIiwidG9hc3QiLCJKb3VybmFsRW50cnkiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJpc1NhdmluZyIsInNldElzU2F2aW5nIiwibGFzdFNhdmVkIiwic2V0TGFzdFNhdmVkIiwic2hvd0RhdGVQaWNrZXIiLCJzZXRTaG93RGF0ZVBpY2tlciIsInNlbGVjdGVkVGFncyIsInNldFNlbGVjdGVkVGFncyIsInNlYXJjaFF1ZXJ5Iiwic2V0U2VhcmNoUXVlcnkiLCJzaG93U2VhcmNoIiwic2V0U2hvd1NlYXJjaCIsImpvdXJuYWwiLCJzZXRKb3VybmFsRW50cnkiLCJzZXRTZWxlY3RlZERhdGUiLCJzYXZlVG9TdG9yZSIsImpvdXJuYWxEYXRlcyIsImF2YWlsYWJsZVRhZ3MiLCJzYXZlZEVudHJ5Iiwic2VsZWN0ZWREYXRlIiwiY29udGVudCIsInRhZ3MiLCJsYXN0VXBkYXRlZCIsImN1cnJlbnRFbnRyeSIsInRyaW0iLCJ0aW1lb3V0SWQiLCJzZXRUaW1lb3V0IiwiaGFuZGxlU2F2ZSIsImNsZWFyVGltZW91dCIsInNob3dGZWVkYmFjayIsInN1Y2Nlc3MiLCJEYXRlIiwidG9JU09TdHJpbmciLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVEYXRlQ2hhbmdlIiwibmV3RGF0ZSIsIm5hdmlnYXRlRGF0ZSIsImRpcmVjdGlvbiIsImN1cnJlbnRJbmRleCIsImluZGV4T2YiLCJsZW5ndGgiLCJoYW5kbGVUYWdUb2dnbGUiLCJ0YWciLCJwcmV2IiwiaW5jbHVkZXMiLCJmaWx0ZXIiLCJ0IiwiZ2V0V29yZENvdW50IiwidGV4dCIsInJlcGxhY2UiLCJzcGxpdCIsIndvcmQiLCJnZXRDaGFyYWN0ZXJDb3VudCIsImV4cG9ydEVudHJ5IiwiYmxvYiIsIkJsb2IiLCJ0eXBlIiwidXJsIiwiVVJMIiwiY3JlYXRlT2JqZWN0VVJMIiwiYSIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsImhyZWYiLCJkb3dubG9hZCIsImJvZHkiLCJhcHBlbmRDaGlsZCIsImNsaWNrIiwicmVtb3ZlQ2hpbGQiLCJyZXZva2VPYmplY3RVUkwiLCJ2YXJpYW50IiwiY2xhc3NOYW1lIiwiZGl2IiwicCIsInNpemUiLCJpY29uIiwib25DbGljayIsImRpc2FibGVkIiwidGl0bGUiLCJsb2FkaW5nIiwiaW5pdGlhbCIsImhlaWdodCIsIm9wYWNpdHkiLCJhbmltYXRlIiwiZXhpdCIsImlucHV0IiwicGxhY2Vob2xkZXIiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImJ1dHRvbiIsInNwYW4iLCJ5IiwiaDMiLCJtYXAiLCJkYXRlIiwidGV4dGFyZWEiLCJyZWYiLCJ0ZXh0YXJlYVJlZiIsImVudHJ5Iiwic2V0RW50cnkiLCJzdHlsZSIsIm1pbkhlaWdodCIsInRvTG9jYWxlVGltZVN0cmluZyIsInNjYWxlIiwic3Ryb25nIiwidWwiLCJsaSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalEntry.js\n"));

/***/ })

});