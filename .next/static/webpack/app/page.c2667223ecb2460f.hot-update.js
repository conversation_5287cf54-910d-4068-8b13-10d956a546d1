"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.js\");\n/* harmony import */ var _components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DailyChecklist */ \"(app-pages-browser)/./src/components/DailyChecklist.js\");\n/* harmony import */ var _components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/JournalEntry */ \"(app-pages-browser)/./src/components/JournalEntry.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _tabs_find;\n    _s();\n    const { ui, setActiveTab, updateStats } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore)();\n    const stats = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats)();\n    const [localStats, setLocalStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalChecklistDays: 0,\n        totalJournalEntries: 0,\n        currentStreak: 0,\n        weeklyGoal: 5,\n        completionRate: 0\n    });\n    // Load and update statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checklistDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllChecklistDates)();\n        const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllJournalDates)();\n        const newStats = {\n            totalChecklistDays: checklistDates.length,\n            totalJournalEntries: journalDates.length,\n            currentStreak: calculateStreak(checklistDates),\n            weeklyGoal: 5,\n            completionRate: checklistDates.length > 0 ? journalDates.length / checklistDates.length * 100 : 0\n        };\n        setLocalStats(newStats);\n        updateStats();\n    }, [\n        updateStats\n    ]);\n    const calculateStreak = (dates)=>{\n        if (dates.length === 0) return 0;\n        const sortedDates = dates.sort().reverse();\n        let streak = 0;\n        const today = new Date();\n        for(let i = 0; i < sortedDates.length; i++){\n            const date = new Date(sortedDates[i]);\n            const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24));\n            if (daysDiff === i) {\n                streak++;\n            } else {\n                break;\n            }\n        }\n        return streak;\n    };\n    const handleExportData = ()=>{\n        try {\n            const data = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.exportAllData)();\n            if (data) {\n                const blob = new Blob([\n                    JSON.stringify(data, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                a.download = \"limitless-options-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"Data exported successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Export error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"Failed to export data\");\n        }\n    };\n    const tabs = [\n        {\n            id: \"checklist\",\n            label: \"Trading Checklist\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Pre-trade analysis and criteria\",\n            color: \"from-primary-500 to-primary-600\"\n        },\n        {\n            id: \"journal\",\n            label: \"Daily Journal\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Trading thoughts and analysis\",\n            color: \"from-accent-500 to-accent-600\"\n        },\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Performance insights and trends\",\n            color: \"from-success-500 to-success-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-4xl md:text-5xl font-bold gradient-text mb-4\",\n                                        children: \"Welcome to Your Trading Hub\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-slate-600 max-w-2xl mx-auto\",\n                                        children: \"Stay disciplined, track your progress, and improve your trading with our professional checklist and journal system.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-slate-800 mb-1\",\n                                                children: stats.totalChecklistDays\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600\",\n                                                children: \"Checklist Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 155,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-accent-500 to-accent-600 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-slate-800 mb-1\",\n                                                children: stats.totalJournalEntries\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600\",\n                                                children: \"Journal Entries\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        className: \"card text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-2xl font-bold text-slate-800 mb-1\",\n                                                children: stats.currentStreak\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 180,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600\",\n                                                children: \"Day Streak\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 181,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-white/80 backdrop-blur-lg rounded-xl p-1 border border-white/20 shadow-lg\",\n                                        children: tabs.map((tab)=>{\n                                            const Icon = tab.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"\\n                      relative flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300\\n                      \".concat(activeTab === tab.id ? \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg\" : \"text-slate-600 hover:text-blue-600 hover:bg-blue-50\", \"\\n                    \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: tab.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 210,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: tab.label.split(\" \")[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 211,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === tab.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                        layoutId: \"activeTab\",\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg -z-10\",\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            bounce: 0.2,\n                                                            duration: 0.6\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 198,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExportData,\n                                        className: \"btn-secondary flex items-center space-x-2 text-sm mt-4 sm:mt-0\",\n                                        title: \"Export all data as backup\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export Data\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600\",\n                                    children: (_tabs_find = tabs.find((tab)=>tab.id === activeTab)) === null || _tabs_find === void 0 ? void 0 : _tabs_find.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            activeTab === \"checklist\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 252,\n                                columnNumber: 41\n                            }, this),\n                            activeTab === \"journal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 253,\n                                columnNumber: 39\n                            }, this)\n                        ]\n                    }, activeTab, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.footer, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        className: \"mt-16 text-center text-slate-500 text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Limitless Options. Built for professional traders who demand excellence.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 257,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"UXyidnzTxP1HYcHU0C+MxLMqAyI=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});