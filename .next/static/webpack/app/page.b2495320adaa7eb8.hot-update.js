"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js":
/*!******************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/check-circle.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ CheckCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst CheckCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"CheckCircle\", [\n  [\"path\", { d: \"M22 11.08V12a10 10 0 1 1-5.93-9.14\", key: \"g774vq\" }],\n  [\"path\", { d: \"m9 11 3 3L22 4\", key: \"1pflzl\" }]\n]);\n\n\n//# sourceMappingURL=check-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvY2hlY2stY2lyY2xlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsb0JBQW9CLGdFQUFnQjtBQUNwQyxhQUFhLHdEQUF3RDtBQUNyRSxhQUFhLG9DQUFvQztBQUNqRDs7QUFFa0M7QUFDbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGVjay1jaXJjbGUuanM/ZGQyNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IENoZWNrQ2lyY2xlID0gY3JlYXRlTHVjaWRlSWNvbihcIkNoZWNrQ2lyY2xlXCIsIFtcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTIyIDExLjA4VjEyYTEwIDEwIDAgMSAxLTUuOTMtOS4xNFwiLCBrZXk6IFwiZzc3NHZxXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIm05IDExIDMgM0wyMiA0XCIsIGtleTogXCIxcGZsemxcIiB9XVxuXSk7XG5cbmV4cG9ydCB7IENoZWNrQ2lyY2xlIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNoZWNrLWNpcmNsZS5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/info.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Info; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Info = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Info\", [\n  [\"circle\", { cx: \"12\", cy: \"12\", r: \"10\", key: \"1mglay\" }],\n  [\"path\", { d: \"M12 16v-4\", key: \"1dtifu\" }],\n  [\"path\", { d: \"M12 8h.01\", key: \"e9boi3\" }]\n]);\n\n\n//# sourceMappingURL=info.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvaW5mby5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0Q7O0FBRXRELGFBQWEsZ0VBQWdCO0FBQzdCLGVBQWUsNENBQTRDO0FBQzNELGFBQWEsK0JBQStCO0FBQzVDLGFBQWEsK0JBQStCO0FBQzVDOztBQUUyQjtBQUMzQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2luZm8uanM/YzM0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IEluZm8gPSBjcmVhdGVMdWNpZGVJY29uKFwiSW5mb1wiLCBbXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjEyXCIsIGN5OiBcIjEyXCIsIHI6IFwiMTBcIiwga2V5OiBcIjFtZ2xheVwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTIgMTZ2LTRcIiwga2V5OiBcIjFkdGlmdVwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTIgOGguMDFcIiwga2V5OiBcImU5Ym9pM1wiIH1dXG5dKTtcblxuZXhwb3J0IHsgSW5mbyBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmZvLmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/ThemeToggle */ \"(app-pages-browser)/./src/components/ui/ThemeToggle.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _features_NotificationCenter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./features/NotificationCenter */ \"(app-pages-browser)/./src/components/features/NotificationCenter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const today = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayKey)();\n    const formattedDate = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.formatDate)(today);\n    const checklist = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist)();\n    const notifications = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const { showMobileMenu, toggleMobileMenu, toggleModal } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const unreadNotifications = notifications.filter((n)=>!n.read).length;\n    const checkedCount = Object.values(checklist.items).filter(Boolean).length;\n    const isReadyToTrade = checkedCount >= 3;\n    const isSessionActive = checklist.isSessionActive;\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Calculate session duration\n    const getSessionDuration = ()=>{\n        if (!checklist.sessionStartTime) return null;\n        const start = new Date(checklist.sessionStartTime);\n        const now = new Date();\n        const diff = Math.floor((now - start) / 1000 / 60) // minutes\n        ;\n        const hours = Math.floor(diff / 60);\n        const minutes = diff % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(minutes, \"m\") : \"\".concat(minutes, \"m\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                className: \"bg-white/80 dark:bg-dark-900/80 backdrop-blur-lg border-b border-gray-200/20 dark:border-dark-700/20 sticky top-0 z-50\",\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"flex items-center space-x-3\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-10 h-10 lg:w-12 lg:h-12 rounded-xl overflow-hidden shadow-lg ring-2 ring-primary-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/LimitlessLogo.jpg\",\n                                            alt: \"Limitless Options Logo\",\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl lg:text-2xl font-bold bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent\",\n                                                children: \"Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 84,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs lg:text-sm text-gray-600 dark:text-gray-400 font-medium\",\n                                                children: \"Trading Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 87,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                    children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                        hour: \"2-digit\",\n                                                        minute: \"2-digit\",\n                                                        hour12: true\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: formattedDate.split(\",\")[0]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"\\n                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                animate: isReadyToTrade ? {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                } : {},\n                                                transition: {\n                                                    duration: 0.5,\n                                                    repeat: isReadyToTrade ? Infinity : 0,\n                                                    repeatDelay: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 124,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 113,\n                                                columnNumber: 17\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"flex items-center space-x-2 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 135,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getSessionDuration()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 130,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 112,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>toggleModal(\"notifications\"),\n                                                        className: \"relative\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        className: \"absolute -top-1 -right-1 w-5 h-5 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                        initial: {\n                                                            scale: 0\n                                                        },\n                                                        animate: {\n                                                            scale: 1\n                                                        },\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            stiffness: 500,\n                                                            damping: 30\n                                                        },\n                                                        children: unreadNotifications\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                variant: \"ghost\",\n                                                onClick: ()=>toggleModal(\"settings\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 167,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"scale-75\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        icon: showMobileMenu ? _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                        variant: \"ghost\",\n                                        onClick: toggleMobileMenu\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: toggleMobileMenu,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"absolute top-16 right-4 left-4 bg-white dark:bg-dark-800 rounded-2xl shadow-xl border border-gray-200 dark:border-dark-700 p-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\",\n                                                    hour12: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 208,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: formattedDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                    flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 221,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Session: \",\n                                                            getSessionDuration()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 233,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"notifications\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: [\n                                            \"Notifications\",\n                                            unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: unreadNotifications\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 255,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"stats\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Statistics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 261,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"settings\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 198,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 191,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"Ighfub8Jps5mjwspJSxPnLpGni0=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/features/NotificationCenter.js":
/*!*******************************************************!*\
  !*** ./src/components/features/NotificationCenter.js ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NotificationCenter; },\n/* harmony export */   useNotificationSystem: function() { return /* binding */ useNotificationSystem; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bell,Check,CheckCircle,Clock,Filter,Info,Settings,Trash2,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/**\n * Notification Center Component\n * Manage and display notifications, reminders, and alerts\n */ /* __next_internal_client_entry_do_not_use__ default,useNotificationSystem auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NotificationCenter(param) {\n    let { isOpen, onClose } = param;\n    _s();\n    const [filter, setFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const { addNotification, markNotificationRead, clearNotifications } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const notifications = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const filteredNotifications = notifications.filter((notification)=>{\n        if (filter === \"all\") return true;\n        if (filter === \"unread\") return !notification.read;\n        return notification.type === filter;\n    });\n    const unreadCount = notifications.filter((n)=>!n.read).length;\n    const getNotificationIcon = (type)=>{\n        switch(type){\n            case \"success\":\n                return _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"warning\":\n                return _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"error\":\n                return _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"reminder\":\n                return _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getNotificationColor = (type)=>{\n        switch(type){\n            case \"success\":\n                return \"text-green-600 bg-green-100 dark:bg-green-900/30\";\n            case \"warning\":\n                return \"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30\";\n            case \"error\":\n                return \"text-red-600 bg-red-100 dark:bg-red-900/30\";\n            case \"reminder\":\n                return \"text-blue-600 bg-blue-100 dark:bg-blue-900/30\";\n            default:\n                return \"text-gray-600 bg-gray-100 dark:bg-gray-900/30\";\n        }\n    };\n    const handleMarkAllRead = ()=>{\n        notifications.forEach((notification)=>{\n            if (!notification.read) {\n                markNotificationRead(notification.id);\n            }\n        });\n    };\n    if (!isOpen) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_10__.AnimatePresence, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n            className: \"fixed inset-0 z-50 flex items-start justify-end p-4 bg-black/20 backdrop-blur-sm\",\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1\n            },\n            exit: {\n                opacity: 0\n            },\n            onClick: onClose,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                className: \"w-full max-w-md mt-16\",\n                initial: {\n                    opacity: 0,\n                    x: 300\n                },\n                animate: {\n                    opacity: 1,\n                    x: 0\n                },\n                exit: {\n                    opacity: 0,\n                    x: 300\n                },\n                onClick: (e)=>e.stopPropagation(),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    variant: \"glass\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-5 h-5 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                                lineNumber: 103,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                            lineNumber: 102,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                            children: unreadCount\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Notifications\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                    lineNumber: 111,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                            lineNumber: 100,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: onClose\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1 bg-gray-100 dark:bg-dark-800 rounded-lg p-1 mt-4\",\n                                    children: [\n                                        {\n                                            id: \"all\",\n                                            label: \"All\"\n                                        },\n                                        {\n                                            id: \"unread\",\n                                            label: \"Unread\"\n                                        },\n                                        {\n                                            id: \"reminder\",\n                                            label: \"Reminders\"\n                                        }\n                                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setFilter(tab.id),\n                                            className: \"\\n                      flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors\\n                      \".concat(filter === tab.id ? \"bg-white dark:bg-dark-700 text-gray-900 dark:text-white shadow-sm\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white\", \"\\n                    \"),\n                                            children: tab.label\n                                        }, tab.id, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                            lineNumber: 123,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-0\",\n                            children: [\n                                notifications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 pb-4 flex space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                            onClick: handleMarkAllRead,\n                                            disabled: unreadCount === 0,\n                                            children: \"Mark All Read\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                            onClick: clearNotifications,\n                                            children: \"Clear All\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-h-96 overflow-y-auto\",\n                                    children: filteredNotifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 px-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bell_Check_CheckCircle_Clock_Filter_Info_Settings_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-400 mx-auto mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 dark:text-white mb-1\",\n                                                children: \"No notifications\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                lineNumber: 169,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400\",\n                                                children: filter === \"all\" ? \"You're all caught up!\" : \"No \".concat(filter, \" notifications\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                lineNumber: 172,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: filteredNotifications.map((notification)=>{\n                                            const Icon = getNotificationIcon(notification.type);\n                                            const colorClasses = getNotificationColor(notification.type);\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    x: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    x: 0\n                                                },\n                                                className: \"\\n                            p-4 border-l-4 transition-colors cursor-pointer\\n                            \".concat(notification.read ? \"bg-gray-50 dark:bg-dark-800 border-gray-300 dark:border-dark-600\" : \"bg-white dark:bg-dark-700 border-blue-500\", \"\\n                            hover:bg-gray-100 dark:hover:bg-dark-600\\n                          \"),\n                                                onClick: ()=>markNotificationRead(notification.id),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 rounded-lg flex items-center justify-center \".concat(colorClasses),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"\\n                                text-sm font-medium\\n                                \".concat(notification.read ? \"text-gray-600 dark:text-gray-400\" : \"text-gray-900 dark:text-white\", \"\\n                              \"),\n                                                                    children: notification.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                notification.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"\\n                                  text-sm mt-1\\n                                  \".concat(notification.read ? \"text-gray-500 dark:text-gray-500\" : \"text-gray-600 dark:text-gray-400\", \"\\n                                \"),\n                                                                    children: notification.message\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                                    lineNumber: 217,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-gray-400 dark:text-gray-500 mt-2\",\n                                                                    children: new Date(notification.timestamp).toLocaleString()\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                                    lineNumber: 228,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                            lineNumber: 205,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        !notification.read && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-500 rounded-full mt-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, notification.id, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                                lineNumber: 186,\n                                                columnNumber: 25\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                        lineNumber: 180,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                            lineNumber: 140,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n                lineNumber: 89,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/features/NotificationCenter.js\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(NotificationCenter, \"4ROEIvr8IRRcJ5YuIopmlavI/5c=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c = NotificationCenter;\n// Notification Hook for easy usage\nfunction useNotificationSystem() {\n    _s1();\n    const { addNotification } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const notify = {\n        success: (title, message)=>addNotification({\n                type: \"success\",\n                title,\n                message\n            }),\n        warning: (title, message)=>addNotification({\n                type: \"warning\",\n                title,\n                message\n            }),\n        error: (title, message)=>addNotification({\n                type: \"error\",\n                title,\n                message\n            }),\n        info: (title, message)=>addNotification({\n                type: \"info\",\n                title,\n                message\n            }),\n        reminder: (title, message)=>addNotification({\n                type: \"reminder\",\n                title,\n                message\n            })\n    };\n    return notify;\n}\n_s1(useNotificationSystem, \"X+c0quUG8fnBRynpY4CDc4iusPU=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\nvar _c;\n$RefreshReg$(_c, \"NotificationCenter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/features/NotificationCenter.js\n"));

/***/ })

});