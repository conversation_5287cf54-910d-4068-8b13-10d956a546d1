"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/activity.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Activity; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Activity = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Activity\", [\n  [\"path\", { d: \"M22 12h-4l-3 9L9 3l-3 9H2\", key: \"d5dnw9\" }]\n]);\n\n\n//# sourceMappingURL=activity.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvYWN0aXZpdHkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXNEOztBQUV0RCxpQkFBaUIsZ0VBQWdCO0FBQ2pDLGFBQWEsK0NBQStDO0FBQzVEOztBQUUrQjtBQUMvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2ljb25zL2FjdGl2aXR5LmpzP2M1NDgiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZSBsdWNpZGUtcmVhY3QgdjAuMjk0LjAgLSBJU0NcbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBJU0MgbGljZW5zZS5cbiAqIFNlZSB0aGUgTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICovXG5cbmltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24uanMnO1xuXG5jb25zdCBBY3Rpdml0eSA9IGNyZWF0ZUx1Y2lkZUljb24oXCJBY3Rpdml0eVwiLCBbXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yMiAxMmgtNGwtMyA5TDkgM2wtMyA5SDJcIiwga2V5OiBcImQ1ZG53OVwiIH1dXG5dKTtcblxuZXhwb3J0IHsgQWN0aXZpdHkgYXMgZGVmYXVsdCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YWN0aXZpdHkuanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.js\");\n/* harmony import */ var _components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DailyChecklist */ \"(app-pages-browser)/./src/components/DailyChecklist.js\");\n/* harmony import */ var _components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/JournalEntry */ \"(app-pages-browser)/./src/components/JournalEntry.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _tabs_find;\n    _s();\n    const { ui, setActiveTab, updateStats } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore)();\n    const stats = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats)();\n    const [localStats, setLocalStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalChecklistDays: 0,\n        totalJournalEntries: 0,\n        currentStreak: 0,\n        weeklyGoal: 5,\n        completionRate: 0\n    });\n    // Load and update statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checklistDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllChecklistDates)();\n        const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllJournalDates)();\n        const newStats = {\n            totalChecklistDays: checklistDates.length,\n            totalJournalEntries: journalDates.length,\n            currentStreak: calculateStreak(checklistDates),\n            weeklyGoal: 5,\n            completionRate: checklistDates.length > 0 ? journalDates.length / checklistDates.length * 100 : 0\n        };\n        setLocalStats(newStats);\n        updateStats();\n    }, [\n        updateStats\n    ]);\n    const calculateStreak = (dates)=>{\n        if (dates.length === 0) return 0;\n        const sortedDates = dates.sort().reverse();\n        let streak = 0;\n        const today = new Date();\n        for(let i = 0; i < sortedDates.length; i++){\n            const date = new Date(sortedDates[i]);\n            const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24));\n            if (daysDiff === i) {\n                streak++;\n            } else {\n                break;\n            }\n        }\n        return streak;\n    };\n    const handleExportData = ()=>{\n        try {\n            const data = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.exportAllData)();\n            if (data) {\n                const blob = new Blob([\n                    JSON.stringify(data, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                a.download = \"limitless-options-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"Data exported successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Export error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"Failed to export data\");\n        }\n    };\n    const tabs = [\n        {\n            id: \"checklist\",\n            label: \"Trading Checklist\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Pre-trade analysis and criteria\",\n            color: \"from-primary-500 to-primary-600\"\n        },\n        {\n            id: \"journal\",\n            label: \"Daily Journal\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Trading thoughts and analysis\",\n            color: \"from-accent-500 to-accent-600\"\n        },\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Performance insights and trends\",\n            color: \"from-success-500 to-success-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold gradient-text mb-4\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        children: \"Your Trading Command Center\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: \"Master your trading discipline with our comprehensive checklist system, rich journal platform, and advanced analytics dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Trading Days\",\n                                            value: localStats.totalChecklistDays,\n                                            change: localStats.currentStreak > 0 ? \"\".concat(localStats.currentStreak, \" day streak\") : \"Start your streak!\",\n                                            changeType: localStats.currentStreak > 0 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Journal Entries\",\n                                            value: localStats.totalJournalEntries,\n                                            change: \"\".concat(Math.round(localStats.completionRate), \"% completion rate\"),\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : localStats.completionRate >= 50 ? \"neutral\" : \"negative\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Current Streak\",\n                                            value: \"\".concat(localStats.currentStreak, \" days\"),\n                                            change: localStats.currentStreak >= localStats.weeklyGoal ? \"Weekly goal achieved!\" : \"\".concat(localStats.weeklyGoal - localStats.currentStreak, \" to weekly goal\"),\n                                            changeType: localStats.currentStreak >= localStats.weeklyGoal ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Performance\",\n                                            value: \"\".concat(Math.round(localStats.completionRate), \"%\"),\n                                            change: \"Journal completion\",\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.4\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-1 bg-white/80 backdrop-blur-lg rounded-xl p-1 border border-white/20 shadow-lg\",\n                                        children: tabs.map((tab)=>{\n                                            const Icon = tab.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(tab.id),\n                                                className: \"\\n                      relative flex items-center space-x-2 px-6 py-3 rounded-lg font-semibold transition-all duration-300\\n                      \".concat(activeTab === tab.id ? \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg\" : \"text-slate-600 hover:text-blue-600 hover:bg-blue-50\", \"\\n                    \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: tab.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sm:hidden\",\n                                                        children: tab.label.split(\" \")[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    activeTab === tab.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                        layoutId: \"activeTab\",\n                                                        className: \"absolute inset-0 bg-gradient-to-r from-blue-600 to-blue-700 rounded-lg -z-10\",\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            bounce: 0.2,\n                                                            duration: 0.6\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, tab.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleExportData,\n                                        className: \"btn-secondary flex items-center space-x-2 text-sm mt-4 sm:mt-0\",\n                                        title: \"Export all data as backup\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Export Data\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                lineNumber: 260,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 254,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600\",\n                                    children: (_tabs_find = tabs.find((tab)=>tab.id === activeTab)) === null || _tabs_find === void 0 ? void 0 : _tabs_find.description\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            activeTab === \"checklist\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 280,\n                                columnNumber: 41\n                            }, this),\n                            activeTab === \"journal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 281,\n                                columnNumber: 39\n                            }, this)\n                        ]\n                    }, activeTab, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 273,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.footer, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        className: \"mt-16 text-center text-slate-500 text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Limitless Options. Built for professional traders who demand excellence.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"UXyidnzTxP1HYcHU0C+MxLMqAyI=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});