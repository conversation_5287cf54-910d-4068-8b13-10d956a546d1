"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalEntry.js":
/*!****************************************!*\
  !*** ./src/components/JournalEntry.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JournalEntry; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _journal_RichTextEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./journal/RichTextEditor */ \"(app-pages-browser)/./src/components/journal/RichTextEditor.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction JournalEntry() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDatePicker, setShowDatePicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { journal, setJournalEntry, setSelectedDate, saveJournalEntry: saveToStore } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getAllJournalDates)();\n    const availableTags = [\n        \"Analysis\",\n        \"Emotions\",\n        \"Strategy\",\n        \"Lessons\",\n        \"Market\",\n        \"Performance\",\n        \"Goals\"\n    ];\n    // Load journal entry for selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEntry = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.loadJournalEntry)(journal.selectedDate);\n        if (savedEntry) {\n            setJournalEntry(savedEntry.content || \"\");\n            setSelectedTags(savedEntry.tags || []);\n            setLastSaved(savedEntry.lastUpdated);\n        } else {\n            setJournalEntry(\"\");\n            setSelectedTags([]);\n            setLastSaved(null);\n        }\n        setIsLoading(false);\n    }, [\n        journal.selectedDate\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && journal.currentEntry.trim()) {\n            const timeoutId = setTimeout(()=>{\n                handleSave(false) // Silent save\n                ;\n            }, 2000) // Auto-save after 2 seconds of inactivity\n            ;\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        journal.currentEntry,\n        isLoading\n    ]);\n    const handleSave = async function() {\n        let showFeedback = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        if (showFeedback) setIsSaving(true);\n        const success = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.saveJournalEntry)(selectedDate, entry);\n        if (success) {\n            setLastSaved(new Date().toISOString());\n            // Update journal dates if this is a new entry\n            if (!journalDates.includes(selectedDate)) {\n                setJournalDates((prev)=>[\n                        selectedDate,\n                        ...prev\n                    ].sort().reverse());\n            }\n        }\n        if (showFeedback) {\n            setTimeout(()=>setIsSaving(false), 500);\n        }\n    };\n    const handleDateChange = (newDate)=>{\n        setSelectedDate(newDate);\n        setShowDatePicker(false);\n    };\n    const navigateDate = (direction)=>{\n        const currentIndex = journalDates.indexOf(selectedDate);\n        if (direction === \"prev\" && currentIndex < journalDates.length - 1) {\n            setSelectedDate(journalDates[currentIndex + 1]);\n        } else if (direction === \"next\" && currentIndex > 0) {\n            setSelectedDate(journalDates[currentIndex - 1]);\n        }\n    };\n    const getWordCount = ()=>{\n        return entry.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n    };\n    const getCharacterCount = ()=>{\n        return entry.length;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-slate-200 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-64 bg-slate-100 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-slate-800\",\n                                        children: \"Daily Trading Journal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 132,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 text-sm\",\n                                        children: \"Record your thoughts, analysis, and lessons learned\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            journalDates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateDate(\"prev\"),\n                                        disabled: journalDates.indexOf(selectedDate) === journalDates.length - 1,\n                                        className: \"p-2 rounded-lg hover:bg-dark-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        title: \"Previous entry\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateDate(\"next\"),\n                                        disabled: journalDates.indexOf(selectedDate) === 0,\n                                        className: \"p-2 rounded-lg hover:bg-dark-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        title: \"Next entry\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 154,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSave(true),\n                                disabled: isSaving || !entry.trim(),\n                                className: \"\\n              btn-primary flex items-center space-x-2 text-sm\\n              \".concat(isSaving ? \"opacity-75 cursor-not-allowed\" : \"\", \"\\n            \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isSaving ? \"Saving...\" : \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowDatePicker(!showDatePicker),\n                            className: \"flex items-center space-x-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 187,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-blue-700\",\n                                    children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(selectedDate)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        showDatePicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            className: \"absolute top-full left-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 z-10 min-w-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 mb-3\",\n                                        children: \"Select Date\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 202,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDateChange((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)()),\n                                        className: \"\\n                    w-full text-left px-3 py-2 rounded-lg transition-colors mb-2\\n                    \".concat(selectedDate === (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)() ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                  \"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 215,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600\",\n                                                children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)())\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    journalDates.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-slate-600 mb-2 px-3\",\n                                                children: \"Previous Entries\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-48 overflow-y-auto\",\n                                                children: journalDates.map((date)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDateChange(date),\n                                                        className: \"\\n                            w-full text-left px-3 py-2 rounded-lg transition-colors mb-1\\n                            \".concat(selectedDate === date ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                          \"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(date)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                            lineNumber: 238,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, date, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 225,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 195,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: entry,\n                    onChange: (e)=>setEntry(e.target.value),\n                    placeholder: \"What happened in today's trading session? Record your analysis, emotions, lessons learned, and plans for tomorrow...\",\n                    className: \"w-full h-64 p-4 border-2 border-slate-200 rounded-xl resize-none focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-slate-800 placeholder-slate-400\",\n                    style: {\n                        minHeight: \"16rem\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 252,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-slate-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6 text-sm text-slate-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getWordCount(),\n                                    \" words\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 265,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getCharacterCount(),\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            lastSaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Saved \",\n                                            new Date(lastSaved).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            className: \"text-sm text-green-600 font-medium\",\n                            children: \"Auto-saving...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 278,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 276,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            !entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-800 mb-3\",\n                        children: \"Journal Prompts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Market Analysis:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What was the overall market sentiment?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Which zones worked best today?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What patterns did you observe?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Personal Performance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• How did you feel during trades?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What mistakes did you make?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What will you improve tomorrow?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalEntry, \"GoQYXSsDyOsv7ERVEjpR2BxI4Ec=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\n_c = JournalEntry;\nvar _c;\n$RefreshReg$(_c, \"JournalEntry\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalEntry.js\n"));

/***/ })

});