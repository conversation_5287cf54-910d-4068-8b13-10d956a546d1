"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/JournalEntry.js":
/*!****************************************!*\
  !*** ./src/components/JournalEntry.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JournalEntry; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _journal_RichTextEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./journal/RichTextEditor */ \"(app-pages-browser)/./src/components/journal/RichTextEditor.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction JournalEntry() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDatePicker, setShowDatePicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { journal, setJournalEntry, setSelectedDate, saveJournalEntry: saveToStore } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getAllJournalDates)();\n    const availableTags = [\n        \"Analysis\",\n        \"Emotions\",\n        \"Strategy\",\n        \"Lessons\",\n        \"Market\",\n        \"Performance\",\n        \"Goals\"\n    ];\n    // Load journal dates and current entry\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const dates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getAllJournalDates)();\n        setJournalDates(dates);\n        const savedEntry = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.loadJournalEntry)(selectedDate);\n        if (savedEntry) {\n            setEntry(savedEntry.content || \"\");\n            setLastSaved(savedEntry.lastUpdated);\n        } else {\n            setEntry(\"\");\n            setLastSaved(null);\n        }\n        setIsLoading(false);\n    }, [\n        selectedDate\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && entry.trim()) {\n            const timeoutId = setTimeout(()=>{\n                handleSave(false) // Silent save\n                ;\n            }, 2000) // Auto-save after 2 seconds of inactivity\n            ;\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        entry,\n        isLoading\n    ]);\n    const handleSave = async function() {\n        let showFeedback = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        if (showFeedback) setIsSaving(true);\n        const success = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.saveJournalEntry)(selectedDate, entry);\n        if (success) {\n            setLastSaved(new Date().toISOString());\n            // Update journal dates if this is a new entry\n            if (!journalDates.includes(selectedDate)) {\n                setJournalDates((prev)=>[\n                        selectedDate,\n                        ...prev\n                    ].sort().reverse());\n            }\n        }\n        if (showFeedback) {\n            setTimeout(()=>setIsSaving(false), 500);\n        }\n    };\n    const handleDateChange = (newDate)=>{\n        setSelectedDate(newDate);\n        setShowDatePicker(false);\n    };\n    const navigateDate = (direction)=>{\n        const currentIndex = journalDates.indexOf(selectedDate);\n        if (direction === \"prev\" && currentIndex < journalDates.length - 1) {\n            setSelectedDate(journalDates[currentIndex + 1]);\n        } else if (direction === \"next\" && currentIndex > 0) {\n            setSelectedDate(journalDates[currentIndex - 1]);\n        }\n    };\n    const getWordCount = ()=>{\n        return entry.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n    };\n    const getCharacterCount = ()=>{\n        return entry.length;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-slate-200 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-64 bg-slate-100 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"card\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"w-6 h-6 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-bold text-slate-800\",\n                                        children: \"Daily Trading Journal\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 133,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600 text-sm\",\n                                        children: \"Record your thoughts, analysis, and lessons learned\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 132,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            journalDates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateDate(\"prev\"),\n                                        disabled: journalDates.indexOf(selectedDate) === journalDates.length - 1,\n                                        className: \"p-2 rounded-lg hover:bg-dark-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        title: \"Previous entry\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>navigateDate(\"next\"),\n                                        disabled: journalDates.indexOf(selectedDate) === 0,\n                                        className: \"p-2 rounded-lg hover:bg-dark-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                        title: \"Next entry\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleSave(true),\n                                disabled: isSaving || !entry.trim(),\n                                className: \"\\n              btn-primary flex items-center space-x-2 text-sm\\n              \".concat(isSaving ? \"opacity-75 cursor-not-allowed\" : \"\", \"\\n            \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: isSaving ? \"Saving...\" : \"Save\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setShowDatePicker(!showDatePicker),\n                            className: \"flex items-center space-x-2 px-4 py-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"w-4 h-4 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-semibold text-blue-700\",\n                                    children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(selectedDate)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 184,\n                            columnNumber: 11\n                        }, this),\n                        showDatePicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                y: -10\n                            },\n                            className: \"absolute top-full left-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 z-10 min-w-64\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-slate-800 mb-3\",\n                                        children: \"Select Date\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 203,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDateChange((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)()),\n                                        className: \"\\n                    w-full text-left px-3 py-2 rounded-lg transition-colors mb-2\\n                    \".concat(selectedDate === (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)() ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                  \"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"font-medium\",\n                                                children: \"Today\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-slate-600\",\n                                                children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)())\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 217,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 17\n                                    }, this),\n                                    journalDates.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm font-medium text-slate-600 mb-2 px-3\",\n                                                children: \"Previous Entries\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 223,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"max-h-48 overflow-y-auto\",\n                                                children: journalDates.map((date)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDateChange(date),\n                                                        className: \"\\n                            w-full text-left px-3 py-2 rounded-lg transition-colors mb-1\\n                            \".concat(selectedDate === date ? \"bg-blue-100 text-blue-700\" : \"hover:bg-slate-50\", \"\\n                          \"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm\",\n                                                            children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(date)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, date, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 182,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: entry,\n                    onChange: (e)=>setEntry(e.target.value),\n                    placeholder: \"What happened in today's trading session? Record your analysis, emotions, lessons learned, and plans for tomorrow...\",\n                    className: \"w-full h-64 p-4 border-2 border-slate-200 rounded-xl resize-none focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-slate-800 placeholder-slate-400\",\n                    style: {\n                        minHeight: \"16rem\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-slate-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6 text-sm text-slate-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getWordCount(),\n                                    \" words\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getCharacterCount(),\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 267,\n                                columnNumber: 11\n                            }, this),\n                            lastSaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Saved \",\n                                            new Date(lastSaved).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            className: \"text-sm text-green-600 font-medium\",\n                            children: \"Auto-saving...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 264,\n                columnNumber: 7\n            }, this),\n            !entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-800 mb-3\",\n                        children: \"Journal Prompts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 297,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Market Analysis:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What was the overall market sentiment?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Which zones worked best today?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 303,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What patterns did you observe?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 304,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 301,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Personal Performance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• How did you feel during trades?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What mistakes did you make?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What will you improve tomorrow?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 292,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalEntry, \"GoQYXSsDyOsv7ERVEjpR2BxI4Ec=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\n_c = JournalEntry;\nvar _c;\n$RefreshReg$(_c, \"JournalEntry\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalEntry.js\n"));

/***/ })

});