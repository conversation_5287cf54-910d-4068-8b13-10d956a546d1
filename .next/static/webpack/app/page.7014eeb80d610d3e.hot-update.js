"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Header.js":
/*!**********************************!*\
  !*** ./src/components/Header.js ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Header; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bell,BookOpen,Calendar,Menu,Settings,Target,Timer,TrendingUp,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/ThemeToggle */ \"(app-pages-browser)/./src/components/ui/ThemeToggle.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _features_NotificationCenter__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./features/NotificationCenter */ \"(app-pages-browser)/./src/components/features/NotificationCenter.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction Header() {\n    _s();\n    const [currentTime, setCurrentTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Date());\n    const [showNotifications, setShowNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const today = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayKey)();\n    const formattedDate = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.formatDate)(today);\n    const checklist = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist)();\n    const notifications = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    const { showMobileMenu, toggleMobileMenu, toggleModal } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore)();\n    const unreadNotifications = notifications.filter((n)=>!n.read).length;\n    const checkedCount = Object.values(checklist.items).filter(Boolean).length;\n    const isReadyToTrade = checkedCount >= 3;\n    const isSessionActive = checklist.isSessionActive;\n    // Update time every minute\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const timer = setInterval(()=>{\n            setCurrentTime(new Date());\n        }, 60000);\n        return ()=>clearInterval(timer);\n    }, []);\n    // Calculate session duration\n    const getSessionDuration = ()=>{\n        if (!checklist.sessionStartTime) return null;\n        const start = new Date(checklist.sessionStartTime);\n        const now = new Date();\n        const diff = Math.floor((now - start) / 1000 / 60) // minutes\n        ;\n        const hours = Math.floor(diff / 60);\n        const minutes = diff % 60;\n        return hours > 0 ? \"\".concat(hours, \"h \").concat(minutes, \"m\") : \"\".concat(minutes, \"m\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                className: \"bg-white/80 dark:bg-dark-900/80 backdrop-blur-lg border-b border-gray-200/20 dark:border-dark-700/20 sticky top-0 z-50\",\n                initial: {\n                    y: -100\n                },\n                animate: {\n                    y: 0\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between h-16 lg:h-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                className: \"flex items-center space-x-3\",\n                                whileHover: {\n                                    scale: 1.02\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-10 h-10 lg:w-12 lg:h-12 rounded-xl overflow-hidden shadow-lg ring-2 ring-primary-500/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            src: \"/LimitlessLogo.jpg\",\n                                            alt: \"Limitless Options Logo\",\n                                            fill: true,\n                                            className: \"object-cover\",\n                                            priority: true\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-xl lg:text-2xl font-bold bg-gradient-to-r from-primary-600 to-accent-500 bg-clip-text text-transparent\",\n                                                children: \"Limitless Options\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs lg:text-sm text-gray-600 dark:text-gray-400 font-medium\",\n                                                children: \"Trading Hub\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 88,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden lg:flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm font-semibold text-gray-900 dark:text-white\",\n                                                    children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                        hour: \"2-digit\",\n                                                        minute: \"2-digit\",\n                                                        hour12: true\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                    children: formattedDate.split(\",\")[0]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                            lineNumber: 98,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"\\n                    flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                animate: isReadyToTrade ? {\n                                                    scale: [\n                                                        1,\n                                                        1.05,\n                                                        1\n                                                    ]\n                                                } : {},\n                                                transition: {\n                                                    duration: 0.5,\n                                                    repeat: isReadyToTrade ? Infinity : 0,\n                                                    repeatDelay: 3\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                className: \"flex items-center space-x-2 px-3 py-2 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                initial: {\n                                                    opacity: 0,\n                                                    scale: 0.8\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    scale: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: getSessionDuration()\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 131,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                        variant: \"ghost\",\n                                                        onClick: ()=>toggleModal(\"notifications\"),\n                                                        className: \"relative\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 146,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                        className: \"absolute -top-1 -right-1 w-5 h-5 bg-danger-500 text-white text-xs rounded-full flex items-center justify-center\",\n                                                        initial: {\n                                                            scale: 0\n                                                        },\n                                                        animate: {\n                                                            scale: 1\n                                                        },\n                                                        transition: {\n                                                            type: \"spring\",\n                                                            stiffness: 500,\n                                                            damping: 30\n                                                        },\n                                                        children: unreadNotifications\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 145,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                                icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                variant: \"ghost\",\n                                                onClick: ()=>toggleModal(\"settings\")\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:hidden flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"scale-75\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__.IconButton, {\n                                        icon: showMobileMenu ? _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                                        variant: \"ghost\",\n                                        onClick: toggleMobileMenu\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                children: showMobileMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                    className: \"lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm\",\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 1\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    onClick: toggleMobileMenu,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        className: \"absolute top-16 right-4 left-4 bg-white dark:bg-dark-800 rounded-2xl shadow-xl border border-gray-200 dark:border-dark-700 p-6\",\n                        initial: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            scale: 1,\n                            y: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            scale: 0.95,\n                            y: -20\n                        },\n                        onClick: (e)=>e.stopPropagation(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-lg font-semibold text-gray-900 dark:text-white\",\n                                                children: currentTime.toLocaleTimeString(\"en-US\", {\n                                                    hour: \"2-digit\",\n                                                    minute: \"2-digit\",\n                                                    hour12: true\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 209,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                children: formattedDate\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 208,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                    flex items-center justify-center space-x-2 px-4 py-3 rounded-lg text-sm font-medium\\n                    \".concat(isReadyToTrade ? \"bg-success-50 dark:bg-success-900/20 text-success-700 dark:text-success-400\" : \"bg-warning-50 dark:bg-warning-900/20 text-warning-700 dark:text-warning-400\", \"\\n                  \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: isReadyToTrade ? \"Ready to Trade\" : \"\".concat(3 - checkedCount, \" more needed\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, this),\n                                            isSessionActive && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center space-x-2 px-4 py-3 bg-primary-50 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400 rounded-lg text-sm font-medium\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Session: \",\n                                                            getSessionDuration()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                        lineNumber: 236,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 221,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"notifications\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: [\n                                            \"Notifications\",\n                                            unreadNotifications > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-auto bg-danger-500 text-white text-xs px-2 py-1 rounded-full\",\n                                                children: unreadNotifications\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                                lineNumber: 256,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 244,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"stats\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Statistics\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 262,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        variant: \"ghost\",\n                                        fullWidth: true,\n                                        icon: _barrel_optimize_names_BarChart3_Bell_BookOpen_Calendar_Menu_Settings_Target_Timer_TrendingUp_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        onClick: ()=>{\n                                            toggleModal(\"settings\");\n                                            toggleMobileMenu();\n                                        },\n                                        className: \"justify-start\",\n                                        children: \"Settings\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                                lineNumber: 243,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                        lineNumber: 199,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                    lineNumber: 192,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/Header.js\",\n                lineNumber: 190,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Header, \"zNkunBbG0HAPpbu29KHOPKOKPz4=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useChecklist,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useNotifications,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_4__.useStore\n    ];\n});\n_c = Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Header.js\n"));

/***/ })

});