"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.js":
/*!*************************!*\
  !*** ./src/app/page.js ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Header */ \"(app-pages-browser)/./src/components/Header.js\");\n/* harmony import */ var _components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/DailyChecklist */ \"(app-pages-browser)/./src/components/DailyChecklist.js\");\n/* harmony import */ var _components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/JournalEntry */ \"(app-pages-browser)/./src/components/JournalEntry.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Award,BarChart3,BookOpen,Calendar,Download,Settings,Star,Target,Timer,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction HomePage() {\n    var _tabs_find;\n    _s();\n    const { ui, setActiveTab, updateStats } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore)();\n    const stats = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats)();\n    const [localStats, setLocalStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalChecklistDays: 0,\n        totalJournalEntries: 0,\n        currentStreak: 0,\n        weeklyGoal: 5,\n        completionRate: 0\n    });\n    // Load and update statistics\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const checklistDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllChecklistDates)();\n        const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.getAllJournalDates)();\n        const newStats = {\n            totalChecklistDays: checklistDates.length,\n            totalJournalEntries: journalDates.length,\n            currentStreak: calculateStreak(checklistDates),\n            weeklyGoal: 5,\n            completionRate: checklistDates.length > 0 ? journalDates.length / checklistDates.length * 100 : 0\n        };\n        setLocalStats(newStats);\n        updateStats();\n    }, [\n        updateStats\n    ]);\n    const calculateStreak = (dates)=>{\n        if (dates.length === 0) return 0;\n        const sortedDates = dates.sort().reverse();\n        let streak = 0;\n        const today = new Date();\n        for(let i = 0; i < sortedDates.length; i++){\n            const date = new Date(sortedDates[i]);\n            const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24));\n            if (daysDiff === i) {\n                streak++;\n            } else {\n                break;\n            }\n        }\n        return streak;\n    };\n    const handleExportData = ()=>{\n        try {\n            const data = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_5__.exportAllData)();\n            if (data) {\n                const blob = new Blob([\n                    JSON.stringify(data, null, 2)\n                ], {\n                    type: \"application/json\"\n                });\n                const url = URL.createObjectURL(blob);\n                const a = document.createElement(\"a\");\n                a.href = url;\n                a.download = \"limitless-options-backup-\".concat(new Date().toISOString().split(\"T\")[0], \".json\");\n                document.body.appendChild(a);\n                a.click();\n                document.body.removeChild(a);\n                URL.revokeObjectURL(url);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].success(\"Data exported successfully!\");\n            }\n        } catch (error) {\n            console.error(\"Export error:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].error(\"Failed to export data\");\n        }\n    };\n    const tabs = [\n        {\n            id: \"checklist\",\n            label: \"Trading Checklist\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Pre-trade analysis and criteria\",\n            color: \"from-primary-500 to-primary-600\"\n        },\n        {\n            id: \"journal\",\n            label: \"Daily Journal\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            description: \"Trading thoughts and analysis\",\n            color: \"from-accent-500 to-accent-600\"\n        },\n        {\n            id: \"analytics\",\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            description: \"Performance insights and trends\",\n            color: \"from-success-500 to-success-600\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.h1, {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold gradient-text mb-4\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.1\n                                        },\n                                        children: \"Your Trading Command Center\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        className: \"text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.2\n                                        },\n                                        children: \"Master your trading discipline with our comprehensive checklist system, rich journal platform, and advanced analytics dashboard.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Trading Days\",\n                                            value: localStats.totalChecklistDays,\n                                            change: localStats.currentStreak > 0 ? \"\".concat(localStats.currentStreak, \" day streak\") : \"Start your streak!\",\n                                            changeType: localStats.currentStreak > 0 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 156,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.4\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Journal Entries\",\n                                            value: localStats.totalJournalEntries,\n                                            change: \"\".concat(Math.round(localStats.completionRate), \"% completion rate\"),\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : localStats.completionRate >= 50 ? \"neutral\" : \"negative\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Current Streak\",\n                                            value: \"\".concat(localStats.currentStreak, \" days\"),\n                                            change: localStats.currentStreak >= localStats.weeklyGoal ? \"Weekly goal achieved!\" : \"\".concat(localStats.weeklyGoal - localStats.currentStreak, \" to weekly goal\"),\n                                            changeType: localStats.currentStreak >= localStats.weeklyGoal ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: 0.6\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__.StatsCard, {\n                                            title: \"Performance\",\n                                            value: \"\".concat(Math.round(localStats.completionRate), \"%\"),\n                                            change: \"Journal completion\",\n                                            changeType: localStats.completionRate >= 80 ? \"positive\" : \"neutral\",\n                                            icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        transition: {\n                            delay: 0.7\n                        },\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            variant: \"glass\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-center justify-between p-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2 bg-gray-100 dark:bg-dark-800 rounded-xl p-1\",\n                                            children: tabs.map((tab)=>{\n                                                const Icon = tab.icon;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.button, {\n                                                    onClick: ()=>setActiveTab(tab.id),\n                                                    className: \"\\n                        relative flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold transition-all duration-300\\n                        \".concat(ui.activeTab === tab.id ? \"text-white shadow-lg\" : \"text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-dark-700\", \"\\n                      \"),\n                                                    whileHover: {\n                                                        scale: 1.02\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.98\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"hidden sm:inline\",\n                                                            children: tab.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sm:hidden\",\n                                                            children: tab.label.split(\" \")[0]\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 242,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        ui.activeTab === tab.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                                                            layoutId: \"activeTabBg\",\n                                                            className: \"absolute inset-0 bg-gradient-to-r \".concat(tab.color, \" rounded-lg\"),\n                                                            transition: {\n                                                                type: \"spring\",\n                                                                bounce: 0.2,\n                                                                duration: 0.6\n                                                            },\n                                                            style: {\n                                                                zIndex: -1\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, tab.id, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 mt-4 lg:mt-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                    onClick: handleExportData,\n                                                    title: \"Export all data as backup\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"Export\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_Activity_Award_BarChart3_BookOpen_Calendar_Download_Settings_Star_Target_Timer_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                    onClick: ()=>react_hot_toast__WEBPACK_IMPORTED_MODULE_9__[\"default\"].info(\"Settings coming soon!\"),\n                                                    title: \"Settings\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"hidden sm:inline\",\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-6 pb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.p, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        className: \"text-center text-gray-600 dark:text-gray-400\",\n                                        children: (_tabs_find = tabs.find((tab)=>tab.id === ui.activeTab)) === null || _tabs_find === void 0 ? void 0 : _tabs_find.description\n                                    }, ui.activeTab, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            x: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            x: 0\n                        },\n                        exit: {\n                            opacity: 0,\n                            x: -20\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        children: [\n                            activeTab === \"checklist\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DailyChecklist__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 303,\n                                columnNumber: 41\n                            }, this),\n                            activeTab === \"journal\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_JournalEntry__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                                lineNumber: 304,\n                                columnNumber: 39\n                            }, this)\n                        ]\n                    }, activeTab, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_13__.motion.footer, {\n                        initial: {\n                            opacity: 0\n                        },\n                        animate: {\n                            opacity: 1\n                        },\n                        transition: {\n                            delay: 0.6\n                        },\n                        className: \"mt-16 text-center text-slate-500 text-sm\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"\\xa9 2024 Limitless Options. Built for professional traders who demand excellence.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                            lineNumber: 314,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                        lineNumber: 308,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/app/page.js\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"UXyidnzTxP1HYcHU0C+MxLMqAyI=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStore,\n        _store_useStore__WEBPACK_IMPORTED_MODULE_6__.useStats\n    ];\n});\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.js\n"));

/***/ })

});