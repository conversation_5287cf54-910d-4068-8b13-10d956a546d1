"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js":
/*!*********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/tag.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Tag; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\n\n\nconst Tag = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Tag\", [\n  [\n    \"path\",\n    {\n      d: \"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z\",\n      key: \"14b2ls\"\n    }\n  ],\n  [\"path\", { d: \"M7 7h.01\", key: \"7u93v4\" }]\n]);\n\n\n//# sourceMappingURL=tag.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvdGFnLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVzRDs7QUFFdEQsWUFBWSxnRUFBZ0I7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLDhCQUE4QjtBQUMzQzs7QUFFMEI7QUFDMUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy90YWcuanM/MzM1MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC4yOTQuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IFRhZyA9IGNyZWF0ZUx1Y2lkZUljb24oXCJUYWdcIiwgW1xuICBbXG4gICAgXCJwYXRoXCIsXG4gICAge1xuICAgICAgZDogXCJNMTIgMkgydjEwbDkuMjkgOS4yOWMuOTQuOTQgMi40OC45NCAzLjQyIDBsNi41OC02LjU4Yy45NC0uOTQuOTQtMi40OCAwLTMuNDJMMTIgMlpcIixcbiAgICAgIGtleTogXCIxNGIybHNcIlxuICAgIH1cbiAgXSxcbiAgW1wicGF0aFwiLCB7IGQ6IFwiTTcgN2guMDFcIiwga2V5OiBcIjd1OTN2NFwiIH1dXG5dKTtcblxuZXhwb3J0IHsgVGFnIGFzIGRlZmF1bHQgfTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXRhZy5qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/JournalEntry.js":
/*!****************************************!*\
  !*** ./src/components/JournalEntry.js ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ JournalEntry; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/motion.mjs\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,BookOpen,Calendar,ChevronLeft,ChevronRight,Clock,Download,FileText,Filter,Heart,Save,Search,Star,Tag,Target,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.js\");\n/* harmony import */ var _store_useStore__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/useStore */ \"(app-pages-browser)/./src/store/useStore.js\");\n/* harmony import */ var _ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/Button */ \"(app-pages-browser)/./src/components/ui/Button.js\");\n/* harmony import */ var _ui_Card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/Card */ \"(app-pages-browser)/./src/components/ui/Card.js\");\n/* harmony import */ var _journal_RichTextEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./journal/RichTextEditor */ \"(app-pages-browser)/./src/components/journal/RichTextEditor.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction JournalEntry() {\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [lastSaved, setLastSaved] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDatePicker, setShowDatePicker] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [showSearch, setShowSearch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { journal, setJournalEntry, setSelectedDate, saveJournalEntry: saveToStore } = (0,_store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const journalDates = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getAllJournalDates)();\n    const availableTags = [\n        \"Analysis\",\n        \"Emotions\",\n        \"Strategy\",\n        \"Lessons\",\n        \"Market\",\n        \"Performance\",\n        \"Goals\"\n    ];\n    // Load journal entry for selected date\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const savedEntry = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.loadJournalEntry)(journal.selectedDate);\n        if (savedEntry) {\n            setJournalEntry(savedEntry.content || \"\");\n            setSelectedTags(savedEntry.tags || []);\n            setLastSaved(savedEntry.lastUpdated);\n        } else {\n            setJournalEntry(\"\");\n            setSelectedTags([]);\n            setLastSaved(null);\n        }\n        setIsLoading(false);\n    }, [\n        journal.selectedDate\n    ]);\n    // Auto-save functionality\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isLoading && journal.currentEntry.trim()) {\n            const timeoutId = setTimeout(()=>{\n                handleSave(false) // Silent save\n                ;\n            }, 2000) // Auto-save after 2 seconds of inactivity\n            ;\n            return ()=>clearTimeout(timeoutId);\n        }\n    }, [\n        journal.currentEntry,\n        isLoading\n    ]);\n    const handleSave = async function() {\n        let showFeedback = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : true;\n        if (showFeedback) setIsSaving(true);\n        try {\n            const success = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.saveJournalEntry)(journal.selectedDate, journal.currentEntry);\n            saveToStore(journal.selectedDate, journal.currentEntry, selectedTags);\n            if (success) {\n                setLastSaved(new Date().toISOString());\n                if (showFeedback) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Journal entry saved successfully!\");\n                }\n            }\n        } catch (error) {\n            console.error(\"Error saving journal entry:\", error);\n            if (showFeedback) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].error(\"Failed to save journal entry\");\n            }\n        }\n        if (showFeedback) {\n            setTimeout(()=>setIsSaving(false), 500);\n        }\n    };\n    const handleDateChange = (newDate)=>{\n        setSelectedDate(newDate);\n        setShowDatePicker(false);\n    };\n    const navigateDate = (direction)=>{\n        const currentIndex = journalDates.indexOf(journal.selectedDate);\n        if (direction === \"prev\" && currentIndex < journalDates.length - 1) {\n            setSelectedDate(journalDates[currentIndex + 1]);\n        } else if (direction === \"next\" && currentIndex > 0) {\n            setSelectedDate(journalDates[currentIndex - 1]);\n        }\n    };\n    const handleTagToggle = (tag)=>{\n        setSelectedTags((prev)=>prev.includes(tag) ? prev.filter((t)=>t !== tag) : [\n                ...prev,\n                tag\n            ]);\n    };\n    const getWordCount = ()=>{\n        const text = journal.currentEntry.replace(/<[^>]*>/g, \"\") // Strip HTML tags\n        ;\n        return text.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n    };\n    const getCharacterCount = ()=>{\n        return journal.currentEntry.length;\n    };\n    const exportEntry = ()=>{\n        const content = \"# Trading Journal Entry - \".concat((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(journal.selectedDate), \"\\n\\n\").concat(journal.currentEntry.replace(/<[^>]*>/g, \"\"));\n        const blob = new Blob([\n            content\n        ], {\n            type: \"text/markdown\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = \"journal-\".concat(journal.selectedDate, \".md\");\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_7__[\"default\"].success(\"Journal entry exported!\");\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            variant: \"glass\",\n            className: \"animate-pulse\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 dark:bg-dark-700 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 150,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-64 bg-gray-100 dark:bg-dark-800 rounded-lg\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n            lineNumber: 149,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"glass\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-xl flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                                    children: \"Daily Trading Journal\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600 dark:text-gray-400 text-sm mt-1\",\n                                                    children: \"Record your thoughts, analysis, and lessons learned\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            onClick: ()=>setShowSearch(!showSearch),\n                                            className: showSearch ? \"bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                            onClick: exportEntry,\n                                            disabled: !journal.currentEntry.trim(),\n                                            title: \"Export entry\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        journalDates.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                    onClick: ()=>navigateDate(\"prev\"),\n                                                    disabled: journalDates.indexOf(journal.selectedDate) === journalDates.length - 1,\n                                                    title: \"Previous entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    variant: \"ghost\",\n                                                    size: \"sm\",\n                                                    icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                    onClick: ()=>navigateDate(\"next\"),\n                                                    disabled: journalDates.indexOf(journal.selectedDate) === 0,\n                                                    title: \"Next entry\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            variant: \"primary\",\n                                            size: \"sm\",\n                                            icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                            onClick: ()=>handleSave(true),\n                                            disabled: isSaving || !journal.currentEntry.trim(),\n                                            loading: isSaving,\n                                            children: isSaving ? \"Saving...\" : \"Save\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                            children: showSearch && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                initial: {\n                                    height: 0,\n                                    opacity: 0\n                                },\n                                animate: {\n                                    height: \"auto\",\n                                    opacity: 1\n                                },\n                                exit: {\n                                    height: 0,\n                                    opacity: 0\n                                },\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search journal entries...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-dark-700 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                    lineNumber: 240,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 234,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 160,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                variant: \"glass\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_Button__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"secondary\",\n                                        onClick: ()=>setShowDatePicker(!showDatePicker),\n                                        icon: _barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                        className: \"justify-start\",\n                                        children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(journal.selectedDate)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_14__.AnimatePresence, {\n                                        children: showDatePicker && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                scale: 0.95,\n                                                y: -10\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                scale: 1,\n                                                y: 0\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                scale: 0.95,\n                                                y: -10\n                                            },\n                                            className: \"absolute top-full left-0 mt-2 bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 z-20 min-w-80\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-gray-900 dark:text-white mb-3\",\n                                                        children: \"Select Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleDateChange((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)()),\n                                                        className: \"\\n                          w-full text-left px-3 py-2 rounded-lg transition-colors mb-2\\n                          \".concat(journal.selectedDate === (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)() ? \"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400\" : \"hover:bg-gray-50 dark:hover:bg-dark-700\", \"\\n                        \"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Today\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600 dark:text-gray-400\",\n                                                                children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)((0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.getTodayKey)())\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    journalDates.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 px-3\",\n                                                                children: [\n                                                                    \"Previous Entries (\",\n                                                                    journalDates.length,\n                                                                    \")\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"max-h-48 overflow-y-auto scrollbar-hide\",\n                                                                children: journalDates.map((date)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>handleDateChange(date),\n                                                                        className: \"\\n                                  w-full text-left px-3 py-2 rounded-lg transition-colors mb-1\\n                                  \".concat(journal.selectedDate === date ? \"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400\" : \"hover:bg-gray-50 dark:hover:bg-dark-700\", \"\\n                                \"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm\",\n                                                                            children: (0,_utils_storage__WEBPACK_IMPORTED_MODULE_2__.formatDate)(date)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                            lineNumber: 317,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    }, date, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                        lineNumber: 306,\n                                                                        columnNumber: 31\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 280,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                            lineNumber: 274,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 261,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 lg:ml-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 text-gray-500 dark:text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: \"Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 331,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: availableTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.button, {\n                                                onClick: ()=>handleTagToggle(tag),\n                                                className: \"\\n                      px-3 py-1 rounded-full text-xs font-medium transition-all duration-200\\n                      \".concat(selectedTags.includes(tag) ? \"bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 border border-primary-200 dark:border-primary-800\" : \"bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-dark-600 hover:bg-gray-200 dark:hover:bg-dark-600\", \"\\n                    \"),\n                                                whileHover: {\n                                                    scale: 1.05\n                                                },\n                                                whileTap: {\n                                                    scale: 0.95\n                                                },\n                                                children: tag\n                                            }, tag, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 337,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 259,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 258,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 257,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    ref: textareaRef,\n                    value: entry,\n                    onChange: (e)=>setEntry(e.target.value),\n                    placeholder: \"What happened in today's trading session? Record your analysis, emotions, lessons learned, and plans for tomorrow...\",\n                    className: \"w-full h-64 p-4 border-2 border-slate-200 rounded-xl resize-none focus:border-blue-400 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-slate-800 placeholder-slate-400\",\n                    style: {\n                        minHeight: \"16rem\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between pt-4 border-t border-slate-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-6 text-sm text-slate-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getWordCount(),\n                                    \" words\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    getCharacterCount(),\n                                    \" characters\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            lastSaved && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_BookOpen_Calendar_ChevronLeft_ChevronRight_Clock_Download_FileText_Filter_Heart_Save_Search_Star_Tag_Target_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 378,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            \"Saved \",\n                                            new Date(lastSaved).toLocaleTimeString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 379,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 377,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 373,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.8\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1\n                            },\n                            className: \"text-sm text-green-600 font-medium\",\n                            children: \"Auto-saving...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                            lineNumber: 387,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 385,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 372,\n                columnNumber: 7\n            }, this),\n            !entry.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.motion.div, {\n                initial: {\n                    opacity: 0,\n                    y: 20\n                },\n                animate: {\n                    opacity: 1,\n                    y: 0\n                },\n                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-blue-800 mb-3\",\n                        children: \"Journal Prompts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 405,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-3 text-sm text-blue-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Market Analysis:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What was the overall market sentiment?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• Which zones worked best today?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 411,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What patterns did you observe?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 407,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Personal Performance:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 416,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"mt-1 space-y-1 text-blue-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• How did you feel during trades?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What mistakes did you make?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"• What will you improve tomorrow?\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                                lineNumber: 420,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                        lineNumber: 417,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                                lineNumber: 415,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                        lineNumber: 406,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/Limitless Checklist Project/src/components/JournalEntry.js\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(JournalEntry, \"GoQYXSsDyOsv7ERVEjpR2BxI4Ec=\", false, function() {\n    return [\n        _store_useStore__WEBPACK_IMPORTED_MODULE_3__.useStore\n    ];\n});\n_c = JournalEntry;\nvar _c;\n$RefreshReg$(_c, \"JournalEntry\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/JournalEntry.js\n"));

/***/ })

});