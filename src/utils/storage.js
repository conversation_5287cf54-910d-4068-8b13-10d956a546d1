/**
 * Storage utility for managing checklist and journal data
 * Uses cloud storage (Neon PostgreSQL) with localStorage fallback
 */

// Storage keys for localStorage fallback
const STORAGE_KEYS = {
  CHECKLIST: 'limitless_checklist_',
  JOURNAL: 'limitless_journal_',
  SETTINGS: 'limitless_settings'
}

// Cloud storage API endpoints
const API_ENDPOINTS = {
  CHECKLIST: '/api/checklist',
  JOURNAL: '/api/journal',
  COURSE: '/api/course',
  INIT_DB: '/api/init-db'
}

/**
 * Get today's date in YYYY-MM-DD format
 */
export const getTodayKey = () => {
  return new Date().toISOString().split('T')[0]
}

/**
 * Format date for display
 */
export const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

/**
 * Save checklist data for a specific date (Cloud + localStorage)
 */
export const saveChecklistData = async (date, data) => {
  try {
    // Save to cloud first
    const cloudResponse = await fetch(API_ENDPOINTS.CHECKLIST, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ date, data })
    })

    // Always save to localStorage as backup
    const key = `${STORAGE_KEYS.CHECKLIST}${date}`
    localStorage.setItem(key, JSON.stringify({
      ...data,
      lastUpdated: new Date().toISOString()
    }))

    if (cloudResponse.ok) {
      console.log('Checklist saved to cloud successfully')
    } else {
      console.warn('Cloud save failed, using localStorage only')
    }

    return true
  } catch (error) {
    console.error('Error saving checklist data:', error)
    // Still try localStorage as fallback
    try {
      const key = `${STORAGE_KEYS.CHECKLIST}${date}`
      localStorage.setItem(key, JSON.stringify({
        ...data,
        lastUpdated: new Date().toISOString()
      }))
      return true
    } catch (localError) {
      console.error('localStorage fallback failed:', localError)
      return false
    }
  }
}

/**
 * Load checklist data for a specific date (Cloud + localStorage)
 */
export const loadChecklistData = async (date) => {
  try {
    // Try cloud first
    const cloudResponse = await fetch(`${API_ENDPOINTS.CHECKLIST}?date=${date}`)

    if (cloudResponse.ok) {
      const result = await cloudResponse.json()
      if (result.success && result.data) {
        console.log('Checklist loaded from cloud')
        return result.data
      }
    }

    // Fallback to localStorage
    console.log('Loading checklist from localStorage')
    const key = `${STORAGE_KEYS.CHECKLIST}${date}`
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error('Error loading checklist data:', error)
    // Fallback to localStorage
    try {
      const key = `${STORAGE_KEYS.CHECKLIST}${date}`
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : null
    } catch (localError) {
      console.error('localStorage fallback failed:', localError)
      return null
    }
  }
}

/**
 * Save journal entry for a specific date (Cloud + localStorage)
 */
export const saveJournalEntry = async (date, entry, tags = []) => {
  try {
    // Save to cloud first
    const cloudResponse = await fetch(API_ENDPOINTS.JOURNAL, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ date, content: entry, tags })
    })

    // Always save to localStorage as backup
    const key = `${STORAGE_KEYS.JOURNAL}${date}`
    localStorage.setItem(key, JSON.stringify({
      content: entry,
      tags,
      lastUpdated: new Date().toISOString()
    }))

    if (cloudResponse.ok) {
      console.log('Journal saved to cloud successfully')
    } else {
      console.warn('Cloud save failed, using localStorage only')
    }

    return true
  } catch (error) {
    console.error('Error saving journal entry:', error)
    // Fallback to localStorage
    try {
      const key = `${STORAGE_KEYS.JOURNAL}${date}`
      localStorage.setItem(key, JSON.stringify({
        content: entry,
        tags,
        lastUpdated: new Date().toISOString()
      }))
      return true
    } catch (localError) {
      console.error('localStorage fallback failed:', localError)
      return false
    }
  }
}

/**
 * Load journal entry for a specific date (Cloud + localStorage)
 */
export const loadJournalEntry = async (date) => {
  try {
    // Try cloud first
    const cloudResponse = await fetch(`${API_ENDPOINTS.JOURNAL}?date=${date}`)

    if (cloudResponse.ok) {
      const result = await cloudResponse.json()
      if (result.success && result.data) {
        console.log('Journal loaded from cloud')
        return result.data
      }
    }

    // Fallback to localStorage
    console.log('Loading journal from localStorage')
    const key = `${STORAGE_KEYS.JOURNAL}${date}`
    const data = localStorage.getItem(key)
    return data ? JSON.parse(data) : null
  } catch (error) {
    console.error('Error loading journal entry:', error)
    // Fallback to localStorage
    try {
      const key = `${STORAGE_KEYS.JOURNAL}${date}`
      const data = localStorage.getItem(key)
      return data ? JSON.parse(data) : null
    } catch (localError) {
      console.error('localStorage fallback failed:', localError)
      return null
    }
  }
}

/**
 * Get all dates with checklist data
 */
export const getAllChecklistDates = () => {
  try {
    const dates = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(STORAGE_KEYS.CHECKLIST)) {
        const date = key.replace(STORAGE_KEYS.CHECKLIST, '')
        dates.push(date)
      }
    }
    return dates.sort().reverse() // Most recent first
  } catch (error) {
    console.error('Error getting checklist dates:', error)
    return []
  }
}

/**
 * Get all dates with journal entries
 */
export const getAllJournalDates = () => {
  try {
    const dates = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(STORAGE_KEYS.JOURNAL)) {
        const date = key.replace(STORAGE_KEYS.JOURNAL, '')
        dates.push(date)
      }
    }
    return dates.sort().reverse() // Most recent first
  } catch (error) {
    console.error('Error getting journal dates:', error)
    return []
  }
}

/**
 * Export all data for backup
 */
export const exportAllData = () => {
  try {
    const data = {
      checklists: {},
      journals: {},
      exportDate: new Date().toISOString()
    }
    
    // Export checklists
    const checklistDates = getAllChecklistDates()
    checklistDates.forEach(date => {
      data.checklists[date] = loadChecklistData(date)
    })
    
    // Export journals
    const journalDates = getAllJournalDates()
    journalDates.forEach(date => {
      data.journals[date] = loadJournalEntry(date)
    })
    
    return data
  } catch (error) {
    console.error('Error exporting data:', error)
    return null
  }
}

/**
 * Initialize cloud database
 */
export const initializeCloudDatabase = async () => {
  try {
    const response = await fetch(API_ENDPOINTS.INIT_DB, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    })

    if (response.ok) {
      const result = await response.json()
      console.log('Database initialized:', result.message)
      return true
    } else {
      console.error('Failed to initialize database')
      return false
    }
  } catch (error) {
    console.error('Error initializing database:', error)
    return false
  }
}

/**
 * Save course progress to cloud
 */
export const saveCourseProgress = async (courseData) => {
  try {
    const response = await fetch(API_ENDPOINTS.COURSE, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(courseData)
    })

    if (response.ok) {
      console.log('Course progress saved to cloud')
      return true
    } else {
      console.warn('Failed to save course progress to cloud')
      return false
    }
  } catch (error) {
    console.error('Error saving course progress:', error)
    return false
  }
}

/**
 * Load course progress from cloud
 */
export const loadCourseProgress = async () => {
  try {
    const response = await fetch(API_ENDPOINTS.COURSE)

    if (response.ok) {
      const result = await response.json()
      if (result.success && result.data) {
        console.log('Course progress loaded from cloud')
        return result.data
      }
    }

    console.log('No course progress found in cloud')
    return null
  } catch (error) {
    console.error('Error loading course progress:', error)
    return null
  }
}

/**
 * Clear all data (with confirmation)
 */
export const clearAllData = () => {
  try {
    const keys = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && (key.startsWith(STORAGE_KEYS.CHECKLIST) || key.startsWith(STORAGE_KEYS.JOURNAL))) {
        keys.push(key)
      }
    }

    keys.forEach(key => localStorage.removeItem(key))
    return true
  } catch (error) {
    console.error('Error clearing data:', error)
    return false
  }
}
