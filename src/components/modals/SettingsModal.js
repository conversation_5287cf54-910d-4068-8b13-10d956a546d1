/**
 * Settings Modal Component
 * Comprehensive settings for user preferences and customization
 */

'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  X,
  Settings,
  User,
  Bell,
  Palette,
  Globe,
  Shield,
  Download,
  Upload,
  Trash2,
  Save,
  RefreshCw,
  Moon,
  Sun,
  Monitor,
  Volume2,
  VolumeX,
  Database
} from 'lucide-react'
import Button from '../ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useStore } from '@/store/useStore'
import { useTheme } from 'next-themes'
import MigrationPanel from '../migration/MigrationPanel'
import toast from 'react-hot-toast'

export default function SettingsModal({ isOpen, onClose }) {
  const { preferences, setPreferences, clearNotifications } = useStore()
  const { theme, setTheme } = useTheme()
  const [activeTab, setActiveTab] = useState('general')

  const tabs = [
    { id: 'general', label: 'General', icon: Settings },
    { id: 'appearance', label: 'Appearance', icon: Palette },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'migration', label: 'Cloud Migration', icon: Database },
    { id: 'data', label: 'Data & Privacy', icon: Shield }
  ]

  const handlePreferenceChange = (key, value) => {
    setPreferences({ [key]: value })
    toast.success('Settings updated!')
  }

  const handleExportSettings = () => {
    const settings = {
      preferences,
      theme,
      exportDate: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(settings, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `limitless-settings-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Settings exported!')
  }

  const handleResetSettings = () => {
    if (window.confirm('Are you sure you want to reset all settings to default? This action cannot be undone.')) {
      setPreferences({
        language: 'en',
        notifications: true,
        autoSave: true,
        confidenceRating: true,
        sessionTimer: false,
      })
      setTheme('light')
      toast.success('Settings reset to default!')
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="w-full max-w-4xl max-h-[90vh] overflow-hidden"
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Card variant="glass" className="h-full">
            {/* Header */}
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                    <Settings className="w-5 h-5 text-white" />
                  </div>
                  <span>Settings</span>
                </CardTitle>
                <Button variant="ghost" size="sm" icon={X} onClick={onClose} />
              </div>
            </CardHeader>

            <CardContent className="p-0">
              <div className="flex h-96">
                {/* Sidebar */}
                <div className="w-64 border-r border-gray-200 dark:border-dark-700 p-4">
                  <nav className="space-y-2">
                    {tabs.map((tab) => {
                      const Icon = tab.icon
                      return (
                        <button
                          key={tab.id}
                          onClick={() => setActiveTab(tab.id)}
                          className={`
                            w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors
                            ${activeTab === tab.id
                              ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400'
                              : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-dark-700'
                            }
                          `}
                        >
                          <Icon className="w-5 h-5" />
                          <span>{tab.label}</span>
                        </button>
                      )
                    })}
                  </nav>
                </div>

                {/* Content */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {activeTab === 'general' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">General Settings</h3>
                        
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Auto-save</label>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Automatically save your entries</p>
                            </div>
                            <button
                              onClick={() => handlePreferenceChange('autoSave', !preferences.autoSave)}
                              className={`
                                relative w-12 h-6 rounded-full transition-colors
                                ${preferences.autoSave ? 'bg-primary-600' : 'bg-gray-300 dark:bg-dark-600'}
                              `}
                            >
                              <div className={`
                                absolute w-5 h-5 bg-white rounded-full shadow transition-transform top-0.5
                                ${preferences.autoSave ? 'translate-x-6' : 'translate-x-0.5'}
                              `} />
                            </button>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Confidence Rating</label>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Show confidence rating slider</p>
                            </div>
                            <button
                              onClick={() => handlePreferenceChange('confidenceRating', !preferences.confidenceRating)}
                              className={`
                                relative w-12 h-6 rounded-full transition-colors
                                ${preferences.confidenceRating ? 'bg-primary-600' : 'bg-gray-300 dark:bg-dark-600'}
                              `}
                            >
                              <div className={`
                                absolute w-5 h-5 bg-white rounded-full shadow transition-transform top-0.5
                                ${preferences.confidenceRating ? 'translate-x-6' : 'translate-x-0.5'}
                              `} />
                            </button>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Session Timer</label>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Track trading session duration</p>
                            </div>
                            <button
                              onClick={() => handlePreferenceChange('sessionTimer', !preferences.sessionTimer)}
                              className={`
                                relative w-12 h-6 rounded-full transition-colors
                                ${preferences.sessionTimer ? 'bg-primary-600' : 'bg-gray-300 dark:bg-dark-600'}
                              `}
                            >
                              <div className={`
                                absolute w-5 h-5 bg-white rounded-full shadow transition-transform top-0.5
                                ${preferences.sessionTimer ? 'translate-x-6' : 'translate-x-0.5'}
                              `} />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'appearance' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Appearance</h3>
                        
                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block">Theme</label>
                            <div className="grid grid-cols-3 gap-3">
                              {[
                                { value: 'light', label: 'Light', icon: Sun },
                                { value: 'dark', label: 'Dark', icon: Moon },
                                { value: 'system', label: 'System', icon: Monitor }
                              ].map((option) => {
                                const Icon = option.icon
                                return (
                                  <button
                                    key={option.value}
                                    onClick={() => setTheme(option.value)}
                                    className={`
                                      flex flex-col items-center space-y-2 p-4 rounded-lg border-2 transition-colors
                                      ${theme === option.value
                                        ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                                        : 'border-gray-200 dark:border-dark-700 hover:border-gray-300 dark:hover:border-dark-600'
                                      }
                                    `}
                                  >
                                    <Icon className="w-6 h-6" />
                                    <span className="text-sm font-medium">{option.label}</span>
                                  </button>
                                )
                              })}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'notifications' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Notifications</h3>
                        
                        <div className="space-y-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Push Notifications</label>
                              <p className="text-xs text-gray-500 dark:text-gray-400">Receive notifications for reminders</p>
                            </div>
                            <button
                              onClick={() => handlePreferenceChange('notifications', !preferences.notifications)}
                              className={`
                                relative w-12 h-6 rounded-full transition-colors
                                ${preferences.notifications ? 'bg-primary-600' : 'bg-gray-300 dark:bg-dark-600'}
                              `}
                            >
                              <div className={`
                                absolute w-5 h-5 bg-white rounded-full shadow transition-transform top-0.5
                                ${preferences.notifications ? 'translate-x-6' : 'translate-x-0.5'}
                              `} />
                            </button>
                          </div>

                          <Button
                            variant="secondary"
                            icon={Trash2}
                            onClick={() => {
                              clearNotifications()
                              toast.success('All notifications cleared!')
                            }}
                          >
                            Clear All Notifications
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {activeTab === 'migration' && (
                    <div className="space-y-6">
                      <MigrationPanel />
                    </div>
                  )}

                  {activeTab === 'data' && (
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Data & Privacy</h3>
                        
                        <div className="space-y-4">
                          <Button
                            variant="secondary"
                            icon={Download}
                            onClick={handleExportSettings}
                            fullWidth
                          >
                            Export Settings
                          </Button>

                          <Button
                            variant="secondary"
                            icon={RefreshCw}
                            onClick={handleResetSettings}
                            fullWidth
                          >
                            Reset to Default
                          </Button>

                          <div className="p-4 bg-gray-50 dark:bg-dark-800 rounded-lg">
                            <h4 className="font-medium text-gray-900 dark:text-white mb-2">Data Storage</h4>
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              All your data is stored locally in your browser. No data is sent to external servers.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
