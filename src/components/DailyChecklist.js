'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  CheckCircle2,
  AlertTriangle,
  RotateCcw,
  TrendingUp,
  Clock,
  Play,
  Pause,
  Square,
  Target,
  Zap,
  Star,
  Timer,
  Award
} from 'lucide-react'
import ChecklistItem from './ChecklistItem'
import Button from './ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from './ui/Card'
import { useStore, useChecklist } from '@/store/useStore'
import { saveChecklistData, loadChecklistData, getTodayKey } from '@/utils/storage'

// Trading checklist items based on your requirements
const CHECKLIST_ITEMS = [
  {
    id: 'zone_drawn',
    text: 'Zone drawn on 15–30m',
    description: 'Identify and mark key support/resistance zones on 15-30 minute timeframe',
    priority: 'high'
  },
  {
    id: 'price_sweep',
    text: 'Price hits zone — is there a sweep of recent high/low?',
    description: 'Look for liquidity sweeps above/below recent highs or lows',
    priority: 'high'
  },
  {
    id: 'volume_analysis',
    text: 'Is volume rising or fading at the zone?',
    description: 'Analyze volume behavior when price approaches the identified zone',
    priority: 'medium'
  },
  {
    id: 'structure_flip',
    text: 'Is structure flipping? (Break of market structure)',
    description: 'Confirm break of market structure indicating potential trend change',
    priority: 'high'
  },
  {
    id: 'candle_strength',
    text: 'Candle strength: engulfing, rejection wick, etc.',
    description: 'Look for strong reversal patterns like engulfing candles or rejection wicks',
    priority: 'medium'
  },
  {
    id: 'entry_time',
    text: 'Entry time in ideal session (9:30–11am, or 1–2pm)',
    description: 'Trade during high-volume sessions for better price action',
    priority: 'medium'
  },
  {
    id: 'higher_tf_fvg',
    text: 'Higher time FVG or imbalance present?',
    description: 'Check for Fair Value Gaps or imbalances on higher timeframes',
    priority: 'normal'
  }
]

export default function DailyChecklist() {
  const [isLoading, setIsLoading] = useState(true)
  const [lastSaved, setLastSaved] = useState(null)
  const [sessionTime, setSessionTime] = useState(0)

  const {
    checklist,
    updateChecklistItem,
    setConfidenceRating,
    startTradingSession,
    endTradingSession,
    resetDailyChecklist
  } = useStore()

  const today = getTodayKey()
  const checkedCount = Object.values(checklist.items).filter(Boolean).length
  const totalItems = CHECKLIST_ITEMS.length
  const progressPercentage = (checkedCount / totalItems) * 100
  const isReadyToTrade = checkedCount >= 3
  const isSessionActive = checklist.isSessionActive

  // Session timer effect
  useEffect(() => {
    let interval = null
    if (isSessionActive && checklist.sessionStartTime) {
      interval = setInterval(() => {
        const start = new Date(checklist.sessionStartTime)
        const now = new Date()
        setSessionTime(Math.floor((now - start) / 1000))
      }, 1000)
    } else {
      setSessionTime(0)
    }
    return () => clearInterval(interval)
  }, [isSessionActive, checklist.sessionStartTime])

  // Load saved data on component mount
  useEffect(() => {
    const savedData = loadChecklistData(today)
    if (savedData && savedData.checkedItems) {
      // Load into store instead of local state
      Object.entries(savedData.checkedItems).forEach(([itemId, checked]) => {
        updateChecklistItem(itemId, checked)
      })
      setLastSaved(savedData.lastUpdated)
    }
    setIsLoading(false)
  }, [today])

  // Save data whenever checklist changes
  useEffect(() => {
    const saveData = async () => {
      if (!isLoading && Object.keys(checklist.items).length > 0) {
        try {
          const success = await saveChecklistData(today, {
            checkedItems: checklist.items,
            notes: checklist.notes,
            confidenceRating: checklist.confidenceRating
          })
          if (success) {
            setLastSaved(new Date().toISOString())
          }
        } catch (error) {
          console.error('Error saving checklist:', error)
        }
      }
    }

    saveData()
  }, [checklist.items, checklist.notes, checklist.confidenceRating, today, isLoading])

  const handleItemChange = (itemId, checked) => {
    updateChecklistItem(itemId, checked)
  }

  const handleReset = async () => {
    if (window.confirm('Are you sure you want to reset today\'s checklist? This action cannot be undone.')) {
      resetDailyChecklist()
      try {
        await saveChecklistData(today, { checkedItems: {}, notes: {}, confidenceRating: 5 })
        setLastSaved(new Date().toISOString())
      } catch (error) {
        console.error('Error resetting checklist:', error)
      }
    }
  }

  const handleSessionToggle = () => {
    if (isSessionActive) {
      endTradingSession()
    } else {
      startTradingSession()
    }
  }

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }

  if (isLoading) {
    return (
      <Card variant="glass" className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-dark-700 rounded mb-4"></div>
        <div className="space-y-3">
          {[...Array(7)].map((_, i) => (
            <div key={i} className="h-20 bg-gray-100 dark:bg-dark-800 rounded-xl"></div>
          ))}
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card variant="glass">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center">
                  <Target className="w-5 h-5 text-white" />
                </div>
                <span>Pre-Trade Checklist</span>
              </CardTitle>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                Complete at least 3 items before executing trades
              </p>
            </div>

            <div className="flex items-center space-x-2">
              {/* Session Timer */}
              <Button
                variant={isSessionActive ? "danger" : "primary"}
                size="sm"
                icon={isSessionActive ? Square : Play}
                onClick={handleSessionToggle}
                className="hidden sm:flex"
              >
                {isSessionActive ? 'End Session' : 'Start Session'}
              </Button>

              <Button
                variant="secondary"
                size="sm"
                icon={RotateCcw}
                onClick={handleReset}
                title="Reset checklist"
              >
                <span className="hidden sm:inline">Reset</span>
              </Button>
            </div>
          </div>

          {/* Session Status */}
          {isSessionActive && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mt-4 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg border border-primary-200 dark:border-primary-800"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Timer className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                  <span className="text-sm font-medium text-primary-700 dark:text-primary-300">
                    Trading Session Active
                  </span>
                </div>
                <div className="text-lg font-mono font-bold text-primary-600 dark:text-primary-400">
                  {formatTime(sessionTime)}
                </div>
              </div>
            </motion.div>
          )}
        </CardHeader>
      </Card>

      {/* Progress & Confidence Card */}
      <Card variant="glass">
        <CardContent>
          {/* Progress Section */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Progress: {checkedCount}/{totalItems} completed
              </span>
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {Math.round(progressPercentage)}%
              </span>
            </div>

            <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-3 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-primary-500 to-accent-500 rounded-full transition-all duration-500 ease-out"
                initial={{ width: 0 }}
                animate={{ width: `${progressPercentage}%` }}
                transition={{ duration: 0.8, ease: "easeOut" }}
              />
            </div>

            {/* Progress Milestones */}
            <div className="flex justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
              <span className={checkedCount >= 1 ? 'text-success-600 dark:text-success-400' : ''}>
                1 item
              </span>
              <span className={checkedCount >= 3 ? 'text-success-600 dark:text-success-400 font-semibold' : ''}>
                3 items (Ready!)
              </span>
              <span className={checkedCount >= 5 ? 'text-success-600 dark:text-success-400' : ''}>
                5 items
              </span>
              <span className={checkedCount === 7 ? 'text-success-600 dark:text-success-400 font-semibold' : ''}>
                All complete
              </span>
            </div>
          </div>

          {/* Confidence Rating */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <label className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Pre-Trade Confidence Level
              </label>
              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 cursor-pointer transition-colors ${
                      i < checklist.confidenceRating
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300 dark:text-gray-600'
                    }`}
                    onClick={() => setConfidenceRating(i + 1)}
                  />
                ))}
                <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                  {checklist.confidenceRating}/5
                </span>
              </div>
            </div>

            <input
              type="range"
              min="1"
              max="5"
              value={checklist.confidenceRating}
              onChange={(e) => setConfidenceRating(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 dark:bg-dark-700 rounded-lg appearance-none cursor-pointer slider"
            />

            <div className="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
              <span>Low</span>
              <span>Medium</span>
              <span>High</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Trading Status Card */}
      <Card variant={isReadyToTrade ? "default" : "outlined"}>
        <motion.div
          className={`
            p-6 rounded-2xl border-2 transition-all duration-300
            ${isReadyToTrade
              ? 'bg-gradient-to-r from-success-50 to-emerald-50 dark:from-success-900/20 dark:to-emerald-900/20 border-success-200 dark:border-success-700'
              : 'bg-gradient-to-r from-warning-50 to-orange-50 dark:from-warning-900/20 dark:to-orange-900/20 border-warning-200 dark:border-warning-700'
            }
          `}
          animate={isReadyToTrade ? { scale: [1, 1.01, 1] } : { scale: 1 }}
          transition={{ duration: 0.8, repeat: isReadyToTrade ? Infinity : 0, repeatDelay: 4 }}
        >
          <div className="flex items-center space-x-4">
            <motion.div
              className={`
                w-12 h-12 rounded-xl flex items-center justify-center
                ${isReadyToTrade
                  ? 'bg-gradient-to-r from-success-500 to-emerald-500'
                  : 'bg-gradient-to-r from-warning-500 to-orange-500'
                }
              `}
              animate={isReadyToTrade ? { rotate: [0, 5, -5, 0] } : {}}
              transition={{ duration: 0.5 }}
            >
              {isReadyToTrade ? (
                <CheckCircle2 className="w-6 h-6 text-white" />
              ) : (
                <AlertTriangle className="w-6 h-6 text-white" />
              )}
            </motion.div>

            <div className="flex-1">
              <p className={`text-lg font-bold ${isReadyToTrade ? 'text-success-700 dark:text-success-400' : 'text-warning-700 dark:text-warning-400'}`}>
                {isReadyToTrade ? '🎯 Ready to Execute Trades' : '⚠️ Not Ready to Trade'}
              </p>
              <p className={`text-sm ${isReadyToTrade ? 'text-success-600 dark:text-success-500' : 'text-warning-600 dark:text-warning-500'}`}>
                {isReadyToTrade
                  ? `All requirements met! Confidence: ${checklist.confidenceRating}/5 stars`
                  : `Complete ${3 - checkedCount} more item${3 - checkedCount !== 1 ? 's' : ''} to be ready`
                }
              </p>
            </div>

            {/* Execute Trade Button */}
            {isReadyToTrade && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3 }}
              >
                <Button
                  variant="success"
                  icon={Zap}
                  className="shadow-lg hover:shadow-xl"
                  onClick={() => {
                    // This could trigger a trade execution modal or action
                    console.log('Execute trade clicked')
                  }}
                >
                  Execute Trade
                </Button>
              </motion.div>
            )}
          </div>
        </motion.div>
      </Card>

      {/* Checklist Items */}
      <Card variant="glass">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5 text-primary-600 dark:text-primary-400" />
            <span>Trading Criteria</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <AnimatePresence>
              {CHECKLIST_ITEMS.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <ChecklistItem
                    id={item.id}
                    text={item.text}
                    description={item.description}
                    priority={item.priority}
                    checked={checklist.items[item.id] || false}
                    onChange={handleItemChange}
                  />
                </motion.div>
              ))}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Footer Info */}
      {lastSaved && (
        <Card variant="glass" size="sm">
          <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span>Last saved: {new Date(lastSaved).toLocaleTimeString()}</span>
            </div>
            <div className="flex items-center space-x-4">
              <span>{checkedCount}/{totalItems} completed</span>
              <span>Confidence: {checklist.confidenceRating}/5</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
