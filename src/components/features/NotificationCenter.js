/**
 * Notification Center Component
 * Manage and display notifications, reminders, and alerts
 */

'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Bell, 
  X, 
  Check, 
  AlertTriangle, 
  Info, 
  CheckCircle,
  Clock,
  Trash2,
  <PERSON><PERSON><PERSON>,
  Filter
} from 'lucide-react'
import <PERSON>ton from '../ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useStore, useNotifications } from '@/store/useStore'
import { formatDate } from '@/utils/storage'

export default function NotificationCenter({ isOpen, onClose }) {
  const [filter, setFilter] = useState('all')
  const { addNotification, markNotificationRead, clearNotifications } = useStore()
  const notifications = useNotifications()

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true
    if (filter === 'unread') return !notification.read
    return notification.type === filter
  })

  const unreadCount = notifications.filter(n => !n.read).length

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return CheckCircle
      case 'warning':
        return AlertTriangle
      case 'error':
        return AlertTriangle
      case 'reminder':
        return Clock
      default:
        return Info
    }
  }

  const getNotificationColor = (type) => {
    switch (type) {
      case 'success':
        return 'text-green-600 bg-green-100 dark:bg-green-900/30'
      case 'warning':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30'
      case 'error':
        return 'text-red-600 bg-red-100 dark:bg-red-900/30'
      case 'reminder':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30'
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30'
    }
  }

  const handleMarkAllRead = () => {
    notifications.forEach(notification => {
      if (!notification.read) {
        markNotificationRead(notification.id)
      }
    })
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-start justify-end p-4 bg-black/20 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="w-full max-w-md mt-16"
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          onClick={(e) => e.stopPropagation()}
        >
          <Card variant="glass">
            {/* Header */}
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                      <Bell className="w-5 h-5 text-white" />
                    </div>
                    {unreadCount > 0 && (
                      <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {unreadCount}
                      </div>
                    )}
                  </div>
                  <span>Notifications</span>
                </CardTitle>
                <Button variant="ghost" size="sm" icon={X} onClick={onClose} />
              </div>

              {/* Filter Tabs */}
              <div className="flex space-x-1 bg-gray-100 dark:bg-dark-800 rounded-lg p-1 mt-4">
                {[
                  { id: 'all', label: 'All' },
                  { id: 'unread', label: 'Unread' },
                  { id: 'reminder', label: 'Reminders' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setFilter(tab.id)}
                    className={`
                      flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors
                      ${filter === tab.id
                        ? 'bg-white dark:bg-dark-700 text-gray-900 dark:text-white shadow-sm'
                        : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                      }
                    `}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </CardHeader>

            <CardContent className="p-0">
              {/* Actions */}
              {notifications.length > 0 && (
                <div className="px-6 pb-4 flex space-x-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={Check}
                    onClick={handleMarkAllRead}
                    disabled={unreadCount === 0}
                  >
                    Mark All Read
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={Trash2}
                    onClick={clearNotifications}
                  >
                    Clear All
                  </Button>
                </div>
              )}

              {/* Notifications List */}
              <div className="max-h-96 overflow-y-auto">
                {filteredNotifications.length === 0 ? (
                  <div className="text-center py-8 px-6">
                    <Bell className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-1">
                      No notifications
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {filter === 'all' 
                        ? "You're all caught up!" 
                        : `No ${filter} notifications`
                      }
                    </p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {filteredNotifications.map((notification) => {
                      const Icon = getNotificationIcon(notification.type)
                      const colorClasses = getNotificationColor(notification.type)
                      
                      return (
                        <motion.div
                          key={notification.id}
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className={`
                            p-4 border-l-4 transition-colors cursor-pointer
                            ${notification.read 
                              ? 'bg-gray-50 dark:bg-dark-800 border-gray-300 dark:border-dark-600' 
                              : 'bg-white dark:bg-dark-700 border-blue-500'
                            }
                            hover:bg-gray-100 dark:hover:bg-dark-600
                          `}
                          onClick={() => markNotificationRead(notification.id)}
                        >
                          <div className="flex items-start space-x-3">
                            <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${colorClasses}`}>
                              <Icon className="w-4 h-4" />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <h4 className={`
                                text-sm font-medium
                                ${notification.read 
                                  ? 'text-gray-600 dark:text-gray-400' 
                                  : 'text-gray-900 dark:text-white'
                                }
                              `}>
                                {notification.title}
                              </h4>
                              
                              {notification.message && (
                                <p className={`
                                  text-sm mt-1
                                  ${notification.read 
                                    ? 'text-gray-500 dark:text-gray-500' 
                                    : 'text-gray-600 dark:text-gray-400'
                                  }
                                `}>
                                  {notification.message}
                                </p>
                              )}
                              
                              <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">
                                {new Date(notification.timestamp).toLocaleString()}
                              </p>
                            </div>
                            
                            {!notification.read && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                            )}
                          </div>
                        </motion.div>
                      )
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

// Notification Hook for easy usage
export function useNotificationSystem() {
  const { addNotification } = useStore()

  const notify = {
    success: (title, message) => addNotification({ type: 'success', title, message }),
    warning: (title, message) => addNotification({ type: 'warning', title, message }),
    error: (title, message) => addNotification({ type: 'error', title, message }),
    info: (title, message) => addNotification({ type: 'info', title, message }),
    reminder: (title, message) => addNotification({ type: 'reminder', title, message })
  }

  return notify
}
