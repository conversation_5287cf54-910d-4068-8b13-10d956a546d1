/**
 * PWA Install Component
 * Progressive Web App installation prompt and management
 */

'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Download, Smartphone, X, Check } from 'lucide-react'
import Button from '../ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import toast from 'react-hot-toast'

export default function PWAInstall() {
  const [deferredPrompt, setDeferredPrompt] = useState(null)
  const [showInstallPrompt, setShowInstallPrompt] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)
  const [isStandalone, setIsStandalone] = useState(false)

  useEffect(() => {
    // Check if app is already installed
    setIsStandalone(window.matchMedia('(display-mode: standalone)').matches)
    setIsInstalled(localStorage.getItem('pwa-installed') === 'true')

    // Listen for beforeinstallprompt event
    const handleBeforeInstallPrompt = (e) => {
      e.preventDefault()
      setDeferredPrompt(e)
      
      // Show install prompt if not already installed and not dismissed
      if (!isInstalled && !localStorage.getItem('pwa-dismissed')) {
        setShowInstallPrompt(true)
      }
    }

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true)
      setShowInstallPrompt(false)
      localStorage.setItem('pwa-installed', 'true')
      toast.success('App installed successfully!')
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [isInstalled])

  const handleInstall = async () => {
    if (!deferredPrompt) {
      toast.error('Installation not available')
      return
    }

    try {
      deferredPrompt.prompt()
      const { outcome } = await deferredPrompt.userChoice
      
      if (outcome === 'accepted') {
        setIsInstalled(true)
        setShowInstallPrompt(false)
        localStorage.setItem('pwa-installed', 'true')
      } else {
        toast.info('Installation cancelled')
      }
      
      setDeferredPrompt(null)
    } catch (error) {
      console.error('Installation error:', error)
      toast.error('Installation failed')
    }
  }

  const handleDismiss = () => {
    setShowInstallPrompt(false)
    localStorage.setItem('pwa-dismissed', 'true')
  }

  const showManualInstructions = () => {
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
    const isAndroid = /Android/.test(navigator.userAgent)
    
    let instructions = ''
    
    if (isIOS) {
      instructions = 'Tap the Share button in Safari, then "Add to Home Screen"'
    } else if (isAndroid) {
      instructions = 'Tap the menu button in Chrome, then "Add to Home screen"'
    } else {
      instructions = 'Look for the install icon in your browser\'s address bar'
    }
    
    toast.info(instructions, { duration: 6000 })
  }

  // Don't show if already in standalone mode
  if (isStandalone) {
    return null
  }

  return (
    <AnimatePresence>
      {showInstallPrompt && !isInstalled && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          className="fixed bottom-4 left-4 right-4 z-50 max-w-md mx-auto"
        >
          <Card variant="glass" className="border-2 border-primary-200 dark:border-primary-800">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-primary-500 to-primary-600 rounded-xl flex items-center justify-center flex-shrink-0">
                  <Smartphone className="w-5 h-5 text-white" />
                </div>
                
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                    Install Trading Hub
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    Get quick access to your trading tools. Install as an app for the best experience.
                  </p>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="primary"
                      size="sm"
                      icon={Download}
                      onClick={handleInstall}
                    >
                      Install
                    </Button>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={showManualInstructions}
                    >
                      How?
                    </Button>
                  </div>
                </div>
                
                <button
                  onClick={handleDismiss}
                  className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
      
      {/* Install status indicator for installed apps */}
      {isInstalled && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="fixed bottom-4 right-4 z-50"
        >
          <div className="bg-green-500 text-white p-3 rounded-full shadow-lg">
            <Check className="w-5 h-5" />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// PWA Features Component
export function PWAFeatures() {
  const [isOnline, setIsOnline] = useState(true)
  const [installPromptShown, setInstallPromptShown] = useState(false)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const features = [
    {
      title: 'Offline Access',
      description: 'Access your trading data even without internet connection',
      available: true
    },
    {
      title: 'Native App Feel',
      description: 'Full-screen experience with native app-like interface',
      available: true
    },
    {
      title: 'Quick Launch',
      description: 'Launch directly from your home screen or dock',
      available: true
    },
    {
      title: 'Background Sync',
      description: 'Sync your data when connection is restored',
      available: false // Future feature
    }
  ]

  return (
    <Card variant="glass">
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Smartphone className="w-5 h-5 text-white" />
          </div>
          <div>
            <span>Progressive Web App</span>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-normal mt-1">
              Enhanced mobile experience
            </p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Connection Status */}
        <div className={`
          flex items-center space-x-2 p-3 rounded-lg mb-4
          ${isOnline 
            ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400' 
            : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400'
          }
        `}>
          <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-green-500' : 'bg-red-500'}`} />
          <span className="text-sm font-medium">
            {isOnline ? 'Online' : 'Offline'} - {isOnline ? 'All features available' : 'Limited functionality'}
          </span>
        </div>

        {/* Features List */}
        <div className="space-y-3">
          {features.map((feature, index) => (
            <div key={index} className="flex items-start space-x-3">
              <div className={`
                w-5 h-5 rounded-full flex items-center justify-center mt-0.5
                ${feature.available 
                  ? 'bg-green-100 dark:bg-green-900/30' 
                  : 'bg-gray-100 dark:bg-gray-800'
                }
              `}>
                {feature.available ? (
                  <Check className="w-3 h-3 text-green-600 dark:text-green-400" />
                ) : (
                  <div className="w-2 h-2 bg-gray-400 rounded-full" />
                )}
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">
                  {feature.title}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {feature.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Install Button */}
        <div className="mt-6">
          <Button
            variant="primary"
            icon={Download}
            onClick={() => setInstallPromptShown(true)}
            fullWidth
          >
            Install as App
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
