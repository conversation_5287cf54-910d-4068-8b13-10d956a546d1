/**
 * Session Timer Component
 * Track trading session duration with start/stop functionality
 */

'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Play, Pause, Square, Timer, Clock, Target, Zap } from 'lucide-react'
import <PERSON><PERSON> from '../ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card'
import { useStore } from '@/store/useStore'
import toast from 'react-hot-toast'

export default function SessionTimer() {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [sessionDuration, setSessionDuration] = useState(0)
  const [targetDuration, setTargetDuration] = useState(120) // 2 hours in minutes
  
  const { 
    checklist, 
    startTradingSession, 
    endTradingSession 
  } = useStore()

  const isSessionActive = checklist.isSessionActive
  const sessionStartTime = checklist.sessionStartTime

  // Update current time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)
    return () => clearInterval(timer)
  }, [])

  // Calculate session duration
  useEffect(() => {
    if (isSessionActive && sessionStartTime) {
      const timer = setInterval(() => {
        const start = new Date(sessionStartTime)
        const now = new Date()
        const duration = Math.floor((now - start) / 1000 / 60) // minutes
        setSessionDuration(duration)
      }, 1000)
      return () => clearInterval(timer)
    } else {
      setSessionDuration(0)
    }
  }, [isSessionActive, sessionStartTime])

  const handleStartSession = () => {
    startTradingSession()
    toast.success('Trading session started!')
  }

  const handleEndSession = () => {
    endTradingSession()
    toast.success(`Session ended! Duration: ${formatDuration(sessionDuration)}`)
  }

  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours > 0) {
      return `${hours}h ${mins}m`
    }
    return `${mins}m`
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: true
    })
  }

  const getProgressPercentage = () => {
    return Math.min((sessionDuration / targetDuration) * 100, 100)
  }

  const getSessionStatus = () => {
    if (!isSessionActive) return 'inactive'
    if (sessionDuration >= targetDuration) return 'complete'
    if (sessionDuration >= targetDuration * 0.8) return 'warning'
    return 'active'
  }

  const statusColors = {
    inactive: 'from-gray-500 to-gray-600',
    active: 'from-primary-500 to-primary-600',
    warning: 'from-warning-500 to-warning-600',
    complete: 'from-success-500 to-success-600'
  }

  const status = getSessionStatus()

  return (
    <Card variant="glass">
      <CardHeader>
        <CardTitle className="flex items-center space-x-3">
          <div className={`w-10 h-10 bg-gradient-to-r ${statusColors[status]} rounded-xl flex items-center justify-center`}>
            <Timer className="w-5 h-5 text-white" />
          </div>
          <div>
            <span>Trading Session</span>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-normal mt-1">
              Track your active trading time
            </p>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* Current Time Display */}
        <div className="text-center mb-6">
          <div className="text-3xl font-mono font-bold text-gray-900 dark:text-white mb-2">
            {formatTime(currentTime)}
          </div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {currentTime.toLocaleDateString('en-US', { 
              weekday: 'long', 
              month: 'long', 
              day: 'numeric' 
            })}
          </div>
        </div>

        {/* Session Status */}
        <AnimatePresence mode="wait">
          {isSessionActive ? (
            <motion.div
              key="active"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="space-y-4"
            >
              {/* Session Duration */}
              <div className="text-center">
                <div className="text-4xl font-mono font-bold text-primary-600 dark:text-primary-400 mb-2">
                  {formatDuration(sessionDuration)}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  Session Duration
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>Progress</span>
                  <span>{Math.round(getProgressPercentage())}%</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-dark-700 rounded-full h-3">
                  <motion.div
                    className={`h-full bg-gradient-to-r ${statusColors[status]} rounded-full`}
                    initial={{ width: 0 }}
                    animate={{ width: `${getProgressPercentage()}%` }}
                    transition={{ duration: 0.5 }}
                  />
                </div>
                <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  Target: {formatDuration(targetDuration)}
                </div>
              </div>

              {/* Session Info */}
              <div className="grid grid-cols-2 gap-4 text-center">
                <div className="p-3 bg-gray-50 dark:bg-dark-800 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Started</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {new Date(sessionStartTime).toLocaleTimeString('en-US', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-dark-800 rounded-lg">
                  <div className="text-sm text-gray-600 dark:text-gray-400">Remaining</div>
                  <div className="font-semibold text-gray-900 dark:text-white">
                    {formatDuration(Math.max(0, targetDuration - sessionDuration))}
                  </div>
                </div>
              </div>

              {/* End Session Button */}
              <Button
                variant="danger"
                icon={Square}
                onClick={handleEndSession}
                fullWidth
                className="mt-4"
              >
                End Session
              </Button>
            </motion.div>
          ) : (
            <motion.div
              key="inactive"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="text-center space-y-4"
            >
              <div className="py-8">
                <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No Active Session
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Start tracking your trading session to monitor your active trading time
                </p>
              </div>

              {/* Target Duration Selector */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  Target Duration
                </label>
                <select
                  value={targetDuration}
                  onChange={(e) => setTargetDuration(parseInt(e.target.value))}
                  className="w-full p-2 border border-gray-200 dark:border-dark-700 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-white"
                >
                  <option value={60}>1 hour</option>
                  <option value={90}>1.5 hours</option>
                  <option value={120}>2 hours</option>
                  <option value={180}>3 hours</option>
                  <option value={240}>4 hours</option>
                </select>
              </div>

              {/* Start Session Button */}
              <Button
                variant="primary"
                icon={Play}
                onClick={handleStartSession}
                fullWidth
                className="mt-4"
              >
                Start Trading Session
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Quick Stats */}
        {!isSessionActive && (
          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-dark-700">
            <div className="text-center">
              <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
                Optimal Trading Hours
              </div>
              <div className="flex justify-center space-x-4 text-xs">
                <span className="px-2 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded">
                  9:30-11:00 AM
                </span>
                <span className="px-2 py-1 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 rounded">
                  1:00-2:00 PM
                </span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
