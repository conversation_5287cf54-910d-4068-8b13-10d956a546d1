'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  Save,
  Calendar,
  BookOpen,
  Clock,
  Search,
  ChevronLeft,
  ChevronRight,
  Download,
  Upload,
  Tag,
  Filter,
  TrendingUp,
  BarChart3,
  FileText,
  Star,
  Heart,
  Target,
  Zap
} from 'lucide-react'
import { saveJournalEntry, loadJournalEntry, getAllJournalDates, getTodayKey, formatDate } from '@/utils/storage'
import { useStore, useJournal } from '@/store/useStore'
import Button from './ui/Button'
import Card, { CardHeader, CardTitle, CardContent } from './ui/Card'
import RichTextEditor from './journal/RichTextEditor'
import toast from 'react-hot-toast'

export default function JournalEntry() {
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState(null)
  const [showDatePicker, setShowDatePicker] = useState(false)
  const [selectedTags, setSelectedTags] = useState([])
  const [searchQuery, setSearchQuery] = useState('')
  const [showSearch, setShowSearch] = useState(false)

  const {
    journal,
    setJournalEntry,
    setSelectedDate,
    saveJournalEntry: saveToStore
  } = useStore()

  const journalDates = getAllJournalDates()
  const availableTags = ['Analysis', 'Emotions', 'Strategy', 'Lessons', 'Market', 'Performance', 'Goals']

  // Load journal entry for selected date
  useEffect(() => {
    const savedEntry = loadJournalEntry(journal.selectedDate)
    if (savedEntry) {
      setJournalEntry(savedEntry.content || '')
      setSelectedTags(savedEntry.tags || [])
      setLastSaved(savedEntry.lastUpdated)
    } else {
      setJournalEntry('')
      setSelectedTags([])
      setLastSaved(null)
    }
    setIsLoading(false)
  }, [journal.selectedDate])

  // Auto-save functionality
  useEffect(() => {
    if (!isLoading && journal.currentEntry.trim()) {
      const timeoutId = setTimeout(() => {
        handleSave(false) // Silent save
      }, 2000) // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId)
    }
  }, [journal.currentEntry, isLoading])

  const handleSave = async (showFeedback = true) => {
    if (showFeedback) setIsSaving(true)

    try {
      const success = await saveJournalEntry(journal.selectedDate, journal.currentEntry, selectedTags)
      saveToStore(journal.selectedDate, journal.currentEntry, selectedTags)

      if (success) {
        setLastSaved(new Date().toISOString())
        if (showFeedback) {
          toast.success('Journal entry saved successfully!')
        }
      }
    } catch (error) {
      console.error('Error saving journal entry:', error)
      if (showFeedback) {
        toast.error('Failed to save journal entry')
      }
    }

    if (showFeedback) {
      setTimeout(() => setIsSaving(false), 500)
    }
  }

  const handleDateChange = (newDate) => {
    setSelectedDate(newDate)
    setShowDatePicker(false)
  }

  const navigateDate = (direction) => {
    const currentIndex = journalDates.indexOf(journal.selectedDate)
    if (direction === 'prev' && currentIndex < journalDates.length - 1) {
      setSelectedDate(journalDates[currentIndex + 1])
    } else if (direction === 'next' && currentIndex > 0) {
      setSelectedDate(journalDates[currentIndex - 1])
    }
  }

  const handleTagToggle = (tag) => {
    setSelectedTags(prev =>
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    )
  }

  const getWordCount = () => {
    const text = journal.currentEntry.replace(/<[^>]*>/g, '') // Strip HTML tags
    return text.trim().split(/\s+/).filter(word => word.length > 0).length
  }

  const getCharacterCount = () => {
    return journal.currentEntry.length
  }

  const exportEntry = () => {
    const content = `# Trading Journal Entry - ${formatDate(journal.selectedDate)}\n\n${journal.currentEntry.replace(/<[^>]*>/g, '')}`
    const blob = new Blob([content], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `journal-${journal.selectedDate}.md`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Journal entry exported!')
  }

  if (isLoading) {
    return (
      <Card variant="glass" className="animate-pulse">
        <div className="h-8 bg-gray-200 dark:bg-dark-700 rounded mb-4"></div>
        <div className="h-64 bg-gray-100 dark:bg-dark-800 rounded-lg"></div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header Card */}
      <Card variant="glass">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-accent-500 to-accent-600 rounded-xl flex items-center justify-center">
                <BookOpen className="w-5 h-5 text-white" />
              </div>
              <div>
                <CardTitle>Daily Trading Journal</CardTitle>
                <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                  Record your thoughts, analysis, and lessons learned
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* Search Toggle */}
              <Button
                variant="ghost"
                size="sm"
                icon={Search}
                onClick={() => setShowSearch(!showSearch)}
                className={showSearch ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' : ''}
              />

              {/* Export Button */}
              <Button
                variant="ghost"
                size="sm"
                icon={Download}
                onClick={exportEntry}
                disabled={!journal.currentEntry.trim()}
                title="Export entry"
              />

              {/* Date Navigation */}
              {journalDates.length > 1 && (
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    icon={ChevronLeft}
                    onClick={() => navigateDate('prev')}
                    disabled={journalDates.indexOf(journal.selectedDate) === journalDates.length - 1}
                    title="Previous entry"
                  />

                  <Button
                    variant="ghost"
                    size="sm"
                    icon={ChevronRight}
                    onClick={() => navigateDate('next')}
                    disabled={journalDates.indexOf(journal.selectedDate) === 0}
                    title="Next entry"
                  />
                </div>
              )}

              {/* Save Button */}
              <Button
                variant="primary"
                size="sm"
                icon={Save}
                onClick={() => handleSave(true)}
                disabled={isSaving || !journal.currentEntry.trim()}
                loading={isSaving}
              >
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </div>

          {/* Search Bar */}
          <AnimatePresence>
            {showSearch && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="mt-4"
              >
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search journal entries..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-200 dark:border-dark-700 rounded-lg bg-white dark:bg-dark-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </CardHeader>
      </Card>

      {/* Date & Tags Card */}
      <Card variant="glass">
        <CardContent>
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            {/* Date Selector */}
            <div className="relative">
              <Button
                variant="secondary"
                onClick={() => setShowDatePicker(!showDatePicker)}
                icon={Calendar}
                className="justify-start"
              >
                {formatDate(journal.selectedDate)}
              </Button>

              {/* Date Picker Dropdown */}
              <AnimatePresence>
                {showDatePicker && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className="absolute top-full left-0 mt-2 bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-gray-200 dark:border-dark-700 z-20 min-w-80"
                  >
                    <div className="p-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-3">Select Date</h3>

                      {/* Today Option */}
                      <button
                        onClick={() => handleDateChange(getTodayKey())}
                        className={`
                          w-full text-left px-3 py-2 rounded-lg transition-colors mb-2
                          ${journal.selectedDate === getTodayKey()
                            ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400'
                            : 'hover:bg-gray-50 dark:hover:bg-dark-700'
                          }
                        `}
                      >
                        <div className="font-medium">Today</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">{formatDate(getTodayKey())}</div>
                      </button>

                      {/* Previous Entries */}
                      {journalDates.length > 0 && (
                        <div>
                          <div className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2 px-3">
                            Previous Entries ({journalDates.length})
                          </div>
                          <div className="max-h-48 overflow-y-auto scrollbar-hide">
                            {journalDates.map(date => (
                              <button
                                key={date}
                                onClick={() => handleDateChange(date)}
                                className={`
                                  w-full text-left px-3 py-2 rounded-lg transition-colors mb-1
                                  ${journal.selectedDate === date
                                    ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400'
                                    : 'hover:bg-gray-50 dark:hover:bg-dark-700'
                                  }
                                `}
                              >
                                <div className="text-sm">{formatDate(date)}</div>
                              </button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Tags Section */}
            <div className="flex-1 lg:ml-6">
              <div className="flex items-center space-x-2 mb-2">
                <Tag className="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Tags</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {availableTags.map(tag => (
                  <motion.button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`
                      px-3 py-1 rounded-full text-xs font-medium transition-all duration-200
                      ${selectedTags.includes(tag)
                        ? 'bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-400 border border-primary-200 dark:border-primary-800'
                        : 'bg-gray-100 dark:bg-dark-700 text-gray-600 dark:text-gray-400 border border-gray-200 dark:border-dark-600 hover:bg-gray-200 dark:hover:bg-dark-600'
                      }
                    `}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {tag}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rich Text Editor */}
      <Card variant="glass">
        <CardContent className="p-0">
          <RichTextEditor
            value={journal.currentEntry}
            onChange={setJournalEntry}
            placeholder="What happened in today's trading session? Record your analysis, emotions, lessons learned, and plans for tomorrow..."
            autoSave={true}
          />
        </CardContent>
      </Card>

      {/* Stats and Footer */}
      <Card variant="glass" size="sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-1">
              <FileText className="w-4 h-4" />
              <span>{getWordCount()} words</span>
            </div>
            <div className="flex items-center space-x-1">
              <BarChart3 className="w-4 h-4" />
              <span>{getCharacterCount()} characters</span>
            </div>
            {selectedTags.length > 0 && (
              <div className="flex items-center space-x-1">
                <Tag className="w-4 h-4" />
                <span>{selectedTags.length} tags</span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            {lastSaved && (
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>Saved {new Date(lastSaved).toLocaleTimeString()}</span>
              </div>
            )}

            {journal.currentEntry.trim() && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center space-x-1 text-success-600 dark:text-success-400"
              >
                <div className="w-2 h-2 bg-success-500 rounded-full animate-pulse"></div>
                <span>Auto-saving...</span>
              </motion.div>
            )}
          </div>
        </div>
      </Card>

      {/* Journal Prompts */}
      {!journal.currentEntry.trim() && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card variant="gradient">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-primary-700 dark:text-primary-400">
                <Target className="w-5 h-5" />
                <span>Journal Prompts</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-primary-600 dark:text-primary-400" />
                    <span>Market Analysis</span>
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li className="flex items-start space-x-2">
                      <span className="text-primary-500 mt-1">•</span>
                      <span>What was the overall market sentiment?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-primary-500 mt-1">•</span>
                      <span>Which zones worked best today?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-primary-500 mt-1">•</span>
                      <span>What patterns did you observe?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-primary-500 mt-1">•</span>
                      <span>How did volume behave at key levels?</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2">
                    <Heart className="w-4 h-4 text-accent-600 dark:text-accent-400" />
                    <span>Personal Performance</span>
                  </h4>
                  <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    <li className="flex items-start space-x-2">
                      <span className="text-accent-500 mt-1">•</span>
                      <span>How did you feel during trades?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-accent-500 mt-1">•</span>
                      <span>What mistakes did you make?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-accent-500 mt-1">•</span>
                      <span>What will you improve tomorrow?</span>
                    </li>
                    <li className="flex items-start space-x-2">
                      <span className="text-accent-500 mt-1">•</span>
                      <span>Did you follow your trading plan?</span>
                    </li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
