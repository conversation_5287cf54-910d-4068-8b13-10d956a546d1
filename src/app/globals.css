@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');

@layer base {
  * {
    @apply border-gray-200 dark:border-dark-700;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 dark:from-dark-950 dark:via-dark-900 dark:to-dark-800 text-gray-900 dark:text-gray-100 font-sans transition-colors duration-300;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-dark-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-dark-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-dark-500;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-200 dark:bg-primary-800 text-primary-900 dark:text-primary-100;
  }
}

@layer components {
  /* Glass morphism effect */
  .glass-effect {
    @apply bg-white/80 dark:bg-dark-800/80 backdrop-blur-lg border border-white/20 dark:border-dark-600/20 shadow-xl;
  }

  /* Neumorphism effect */
  .neumorphic {
    @apply bg-gray-100 dark:bg-dark-900;
    box-shadow:
      8px 8px 16px rgba(209, 213, 219, 0.3),
      -8px -8px 16px rgba(255, 255, 255, 0.7);
  }

  .dark .neumorphic {
    box-shadow:
      8px 8px 16px rgba(15, 23, 42, 0.5),
      -8px -8px 16px rgba(30, 41, 59, 0.3);
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-600 via-primary-500 to-accent-500 bg-clip-text text-transparent;
  }

  /* Custom range slider */
  .slider {
    @apply appearance-none bg-gray-200 dark:bg-dark-700 rounded-lg h-2 outline-none;
  }

  .slider::-webkit-slider-thumb {
    @apply appearance-none w-5 h-5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full cursor-pointer shadow-lg;
  }

  .slider::-moz-range-thumb {
    @apply w-5 h-5 bg-gradient-to-r from-primary-500 to-primary-600 rounded-full cursor-pointer border-0 shadow-lg;
  }

  /* Loading shimmer effect */
  .shimmer {
    @apply relative overflow-hidden;
  }

  .shimmer::before {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent;
    content: '';
    animation: shimmer 2s infinite;
  }

  .dark .shimmer::before {
    @apply via-gray-700/60;
  }

  /* Floating elements */
  .float {
    animation: float 3s ease-in-out infinite;
  }

  /* Glow effects */
  .glow-primary {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-success {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }

  .glow-warning {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }

  .glow-danger {
    @apply shadow-lg;
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }

  /* Text clamp utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

@layer utilities {
  /* Text shadows */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* Custom animations */
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .animate-bounce-slow {
    animation: bounce 2s infinite;
  }

  .animate-wiggle {
    animation: wiggle 1s ease-in-out infinite;
  }

  /* Backdrop filters */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }

  /* Custom transforms */
  .transform-gpu {
    transform: translateZ(0);
  }

  /* Safe area padding for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 dark:focus:ring-offset-dark-800;
  }

  /* Smooth transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}
