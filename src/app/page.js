'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Header from '@/components/Header'
import DailyChecklist from '@/components/DailyChecklist'
import JournalEntry from '@/components/JournalEntry'
import TradingAnalytics from '@/components/analytics/TradingAnalytics'
import SettingsModal from '@/components/modals/SettingsModal'
import SessionTimer from '@/components/features/SessionTimer'
import {
  BarChart3,
  TrendingUp,
  Calendar,
  Settings,
  Download,
  Upload,
  Target,
  BookOpen,
  Award,
  Zap,
  Timer,
  Star,
  Activity
} from 'lucide-react'
import { exportAllData, getAllChecklistDates, getAllJournalDates } from '@/utils/storage'
import { useStore, useStats, useUI } from '@/store/useStore'
import Button from '@/components/ui/Button'
import Card, { StatsCard } from '@/components/ui/Card'
import toast from 'react-hot-toast'

export default function HomePage() {
  const { ui, setActiveTab, updateStats, toggleModal } = useStore()
  const stats = useStats()
  const [localStats, setLocalStats] = useState({
    totalChecklistDays: 0,
    totalJournalEntries: 0,
    currentStreak: 0,
    weeklyGoal: 5,
    completionRate: 0
  })
  const [showSessionTimer, setShowSessionTimer] = useState(false)

  // Load and update statistics
  useEffect(() => {
    const checklistDates = getAllChecklistDates()
    const journalDates = getAllJournalDates()

    const newStats = {
      totalChecklistDays: checklistDates.length,
      totalJournalEntries: journalDates.length,
      currentStreak: calculateStreak(checklistDates),
      weeklyGoal: 5,
      completionRate: checklistDates.length > 0 ? (journalDates.length / checklistDates.length) * 100 : 0
    }

    setLocalStats(newStats)
    updateStats()
  }, [updateStats])

  const calculateStreak = (dates) => {
    if (dates.length === 0) return 0

    const sortedDates = dates.sort().reverse()
    let streak = 0
    const today = new Date()

    for (let i = 0; i < sortedDates.length; i++) {
      const date = new Date(sortedDates[i])
      const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24))

      if (daysDiff === i) {
        streak++
      } else {
        break
      }
    }

    return streak
  }

  const handleExportData = () => {
    try {
      const data = exportAllData()
      if (data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `limitless-options-backup-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        toast.success('Data exported successfully!')
      }
    } catch (error) {
      console.error('Export error:', error)
      toast.error('Failed to export data')
    }
  }

  const tabs = [
    {
      id: 'checklist',
      label: 'Trading Checklist',
      icon: Target,
      description: 'Pre-trade analysis and criteria',
      color: 'from-primary-500 to-primary-600'
    },
    {
      id: 'journal',
      label: 'Daily Journal',
      icon: BookOpen,
      description: 'Trading thoughts and analysis',
      color: 'from-accent-500 to-accent-600'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      description: 'Performance insights and trends',
      color: 'from-success-500 to-success-600'
    }
  ]

  return (
    <div className="min-h-screen">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 lg:py-8">
        {/* Welcome Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="text-center mb-8">
            <motion.h1
              className="text-4xl md:text-5xl lg:text-6xl font-bold gradient-text mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
            >
              Your Trading Command Center
            </motion.h1>
            <motion.p
              className="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              Master your trading discipline with our comprehensive checklist system,
              rich journal platform, and advanced analytics dashboard.
            </motion.p>
          </div>

          {/* Enhanced Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <StatsCard
                title="Trading Days"
                value={localStats.totalChecklistDays}
                change={localStats.currentStreak > 0 ? `${localStats.currentStreak} day streak` : 'Start your streak!'}
                changeType={localStats.currentStreak > 0 ? 'positive' : 'neutral'}
                icon={Target}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
            >
              <StatsCard
                title="Journal Entries"
                value={localStats.totalJournalEntries}
                change={`${Math.round(localStats.completionRate)}% completion rate`}
                changeType={localStats.completionRate >= 80 ? 'positive' : localStats.completionRate >= 50 ? 'neutral' : 'negative'}
                icon={BookOpen}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <StatsCard
                title="Current Streak"
                value={`${localStats.currentStreak} days`}
                change={localStats.currentStreak >= localStats.weeklyGoal ? 'Weekly goal achieved!' : `${localStats.weeklyGoal - localStats.currentStreak} to weekly goal`}
                changeType={localStats.currentStreak >= localStats.weeklyGoal ? 'positive' : 'neutral'}
                icon={Award}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <StatsCard
                title="Performance"
                value={`${Math.round(localStats.completionRate)}%`}
                change="Journal completion"
                changeType={localStats.completionRate >= 80 ? 'positive' : 'neutral'}
                icon={Activity}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Enhanced Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="mb-8"
        >
          <Card variant="glass">
            <div className="flex flex-col lg:flex-row items-center justify-between p-2">
              <div className="flex space-x-2 bg-gray-100 dark:bg-dark-800 rounded-xl p-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`
                        relative flex items-center space-x-3 px-6 py-3 rounded-lg font-semibold transition-all duration-300
                        ${ui.activeTab === tab.id
                          ? 'text-white shadow-lg'
                          : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-200 dark:hover:bg-dark-700'
                        }
                      `}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Icon className="w-5 h-5" />
                      <span className="hidden sm:inline">{tab.label}</span>
                      <span className="sm:hidden">{tab.label.split(' ')[0]}</span>

                      {ui.activeTab === tab.id && (
                        <motion.div
                          layoutId="activeTabBg"
                          className={`absolute inset-0 bg-gradient-to-r ${tab.color} rounded-lg`}
                          transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                          style={{ zIndex: -1 }}
                        />
                      )}
                    </motion.button>
                  )
                })}
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2 mt-4 lg:mt-0">
                <Button
                  variant="ghost"
                  size="sm"
                  icon={Download}
                  onClick={handleExportData}
                  title="Export all data as backup"
                >
                  <span className="hidden sm:inline">Export</span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  icon={Settings}
                  onClick={() => toast.info('Settings coming soon!')}
                  title="Settings"
                >
                  <span className="hidden sm:inline">Settings</span>
                </Button>
              </div>
            </div>

            {/* Tab Description */}
            <div className="px-6 pb-4">
              <motion.p
                key={ui.activeTab}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-center text-gray-600 dark:text-gray-400"
              >
                {tabs.find(tab => tab.id === ui.activeTab)?.description}
              </motion.p>
            </div>
          </Card>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={ui.activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            {ui.activeTab === 'checklist' && (
              <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                <div className="xl:col-span-3">
                  <DailyChecklist />
                </div>
                <div className="xl:col-span-1">
                  <SessionTimer />
                </div>
              </div>
            )}
            {ui.activeTab === 'journal' && <JournalEntry />}
            {ui.activeTab === 'analytics' && <TradingAnalytics />}
          </motion.div>
        </AnimatePresence>

        {/* Enhanced Footer */}
        <motion.footer
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
          className="mt-16"
        >
          <Card variant="glass" size="sm">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <div className="w-6 h-6 rounded-lg bg-gradient-to-r from-primary-500 to-accent-500 flex items-center justify-center">
                  <Zap className="w-3 h-3 text-white" />
                </div>
                <span className="font-semibold text-gray-900 dark:text-white">
                  Limitless Options
                </span>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Built for professional traders who demand excellence.
              </p>
              <div className="flex items-center justify-center space-x-4 mt-3 text-xs text-gray-500 dark:text-gray-400">
                <span>© 2024 Limitless Options</span>
                <span>•</span>
                <span>Trading Hub v1.0</span>
                <span>•</span>
                <span>Made with ❤️ for traders</span>
              </div>
            </div>
          </Card>
        </motion.footer>

        {/* Modals */}
        <SettingsModal
          isOpen={ui.modals.settings}
          onClose={() => toggleModal('settings')}
        />
      </main>
    </div>
  )
}
