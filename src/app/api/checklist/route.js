/**
 * API Routes for checklist operations
 * Handles saving and loading checklist data from cloud
 */

import { saveChecklistToCloud, loadChecklistFromCloud } from '@/lib/cloudStorage'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { date, data } = await request.json()
    
    if (!date || !data) {
      return NextResponse.json(
        { success: false, error: 'Date and data are required' },
        { status: 400 }
      )
    }
    
    const success = await saveChecklistToCloud(date, data)
    
    if (success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to save checklist' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Checklist save error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    
    if (!date) {
      return NextResponse.json(
        { success: false, error: 'Date parameter is required' },
        { status: 400 }
      )
    }
    
    const data = await loadChecklistFromCloud(date)
    
    return NextResponse.json({ 
      success: true, 
      data: data || null 
    })
  } catch (error) {
    console.error('Checklist load error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
