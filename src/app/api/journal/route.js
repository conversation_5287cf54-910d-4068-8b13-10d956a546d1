/**
 * API Routes for journal operations
 * Handles saving and loading journal entries from cloud
 */

import { saveJournalToCloud, loadJournalFromCloud } from '@/lib/cloudStorage'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const { date, content, tags = [] } = await request.json()
    
    if (!date) {
      return NextResponse.json(
        { success: false, error: 'Date is required' },
        { status: 400 }
      )
    }
    
    const success = await saveJournalToCloud(date, content, tags)
    
    if (success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to save journal entry' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Journal save error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    
    if (!date) {
      return NextResponse.json(
        { success: false, error: 'Date parameter is required' },
        { status: 400 }
      )
    }
    
    const data = await loadJournalFromCloud(date)
    
    return NextResponse.json({ 
      success: true, 
      data: data || null 
    })
  } catch (error) {
    console.error('Journal load error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
