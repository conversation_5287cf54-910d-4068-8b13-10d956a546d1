/**
 * API Route for real analytics data
 * Returns actual user statistics from database
 */

import { getUserAnalytics, getPerformanceTrend } from '@/lib/analytics'
import { NextResponse } from 'next/server'

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const days = parseInt(searchParams.get('days')) || 30
    const type = searchParams.get('type') || 'summary'
    const userId = 1 // Default user ID

    if (type === 'trend') {
      const trendData = await getPerformanceTrend(userId, days)
      return NextResponse.json({
        success: true,
        data: trendData
      })
    }

    const analytics = await getUserAnalytics(userId, days)
    
    return NextResponse.json({
      success: true,
      data: analytics
    })
  } catch (error) {
    console.error('Analytics API error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message 
      },
      { status: 500 }
    )
  }
}
