/**
 * API Route to initialize database tables
 * Simple endpoint to set up the database schema
 */

import { initializeDatabase } from '@/lib/db'
import { NextResponse } from 'next/server'

export async function POST() {
  try {
    await initializeDatabase()
    return NextResponse.json({ 
      success: true, 
      message: 'Database initialized successfully' 
    })
  } catch (error) {
    console.error('Database initialization error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
