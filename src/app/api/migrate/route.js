/**
 * API Route for database migration
 * <PERSON>les creating tables and migrating localStorage data
 */

import { migrateLocalStorageToCloud, checkMigrationStatus } from '@/lib/migration'
import { NextResponse } from 'next/server'

export async function POST() {
  try {
    console.log('🚀 Starting database migration...')
    
    const migrationResult = await migrateLocalStorageToCloud()
    
    return NextResponse.json({
      success: true,
      message: 'Migration completed successfully',
      data: migrationResult
    })
  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message,
        details: 'Failed to migrate data to cloud database'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const status = await checkMigrationStatus()
    
    return NextResponse.json({
      success: true,
      data: status
    })
  } catch (error) {
    console.error('Migration status check error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error.message 
      },
      { status: 500 }
    )
  }
}
