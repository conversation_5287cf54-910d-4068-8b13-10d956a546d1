/**
 * API Routes for course progress operations
 * Handles saving and loading course data from cloud
 */

import { saveCourseProgressToCloud, loadCourseProgressFromCloud } from '@/lib/cloudStorage'
import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const courseData = await request.json()
    
    if (!courseData) {
      return NextResponse.json(
        { success: false, error: 'Course data is required' },
        { status: 400 }
      )
    }
    
    const success = await saveCourseProgressToCloud(courseData)
    
    if (success) {
      return NextResponse.json({ success: true })
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to save course progress' },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Course save error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}

export async function GET() {
  try {
    const data = await loadCourseProgressFromCloud()
    
    return NextResponse.json({ 
      success: true, 
      data: data || null 
    })
  } catch (error) {
    console.error('Course load error:', error)
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    )
  }
}
