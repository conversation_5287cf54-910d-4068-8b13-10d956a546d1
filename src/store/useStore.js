/**
 * Global State Management with <PERSON>ustand
 * Handles all application state including checklist, journal, theme, and user preferences
 */

import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { getTodayKey, formatDate, saveCourseProgress, loadCourseProgress, initializeCloudDatabase } from '@/utils/storage'

// Main application store
export const useStore = create(
  persist(
    (set, get) => ({
      // Theme state
      theme: 'light',
      setTheme: (theme) => set({ theme }),
      
      // User preferences
      preferences: {
        language: 'en',
        notifications: true,
        autoSave: true,
        confidenceRating: true,
        sessionTimer: false,
      },
      setPreferences: (prefs) => set((state) => ({
        preferences: { ...state.preferences, ...prefs }
      })),

      // Checklist state
      checklist: {
        items: {},
        notes: {},
        confidenceRating: 5,
        sessionStartTime: null,
        isSessionActive: false,
      },
      
      updateChecklistItem: (itemId, checked) => set((state) => ({
        checklist: {
          ...state.checklist,
          items: {
            ...state.checklist.items,
            [itemId]: checked
          }
        }
      })),

      updateChecklistNote: (itemId, note) => set((state) => ({
        checklist: {
          ...state.checklist,
          notes: {
            ...state.checklist.notes,
            [itemId]: note
          }
        }
      })),

      setConfidenceRating: (rating) => set((state) => ({
        checklist: {
          ...state.checklist,
          confidenceRating: rating
        }
      })),

      startTradingSession: () => set((state) => ({
        checklist: {
          ...state.checklist,
          sessionStartTime: new Date().toISOString(),
          isSessionActive: true
        }
      })),

      endTradingSession: () => set((state) => ({
        checklist: {
          ...state.checklist,
          sessionStartTime: null,
          isSessionActive: false
        }
      })),

      resetDailyChecklist: () => set((state) => ({
        checklist: {
          ...state.checklist,
          items: {},
          notes: {},
          confidenceRating: 5,
          sessionStartTime: null,
          isSessionActive: false
        }
      })),

      // Journal state
      journal: {
        currentEntry: '',
        selectedDate: getTodayKey(),
        entries: {},
        tags: [],
      },

      setJournalEntry: (content) => set((state) => ({
        journal: {
          ...state.journal,
          currentEntry: content
        }
      })),

      setSelectedDate: (date) => set((state) => ({
        journal: {
          ...state.journal,
          selectedDate: date
        }
      })),

      saveJournalEntry: (date, content, tags = []) => set((state) => ({
        journal: {
          ...state.journal,
          entries: {
            ...state.journal.entries,
            [date]: {
              content,
              tags,
              createdAt: state.journal.entries[date]?.createdAt || new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            }
          }
        }
      })),

      // Statistics and analytics
      stats: {
        totalTradingDays: 0,
        checklistCompletionRate: 0,
        averageConfidenceRating: 0,
        streakCount: 0,
        weeklyStats: {},
        monthlyStats: {},
      },

      updateStats: () => set((state) => {
        const { checklist, journal } = state
        const checklistDates = Object.keys(checklist.items || {})
        const journalDates = Object.keys(journal.entries || {})
        
        // Calculate completion rate
        const completedDays = checklistDates.filter(date => {
          const items = checklist.items[date] || {}
          const completedCount = Object.values(items).filter(Boolean).length
          return completedCount >= 3
        }).length

        return {
          stats: {
            ...state.stats,
            totalTradingDays: Math.max(checklistDates.length, journalDates.length),
            checklistCompletionRate: checklistDates.length > 0 ? (completedDays / checklistDates.length) * 100 : 0,
            streakCount: calculateStreak(checklistDates),
          }
        }
      }),

      // UI state
      ui: {
        activeTab: 'checklist',
        showMobileMenu: false,
        showNotifications: false,
        isLoading: false,
        modals: {
          settings: false,
          export: false,
          customChecklist: false,
        }
      },

      setActiveTab: (tab) => set((state) => ({
        ui: { ...state.ui, activeTab: tab }
      })),

      toggleMobileMenu: () => set((state) => ({
        ui: { ...state.ui, showMobileMenu: !state.ui.showMobileMenu }
      })),

      toggleModal: (modalName) => set((state) => ({
        ui: {
          ...state.ui,
          modals: {
            ...state.ui.modals,
            [modalName]: !state.ui.modals[modalName]
          }
        }
      })),

      setLoading: (isLoading) => set((state) => ({
        ui: { ...state.ui, isLoading }
      })),

      // Course state
      course: {
        currentModule: 1,
        currentLesson: 1,
        completedLessons: [],
        completedModules: [],
        progress: 0,
        achievements: [],
        lastAccessed: null,
        quizScores: {},
        bookmarks: [],
        notes: {}
      },

      setCourseModule: (moduleId) => set((state) => ({
        course: {
          ...state.course,
          currentModule: moduleId,
          currentLesson: 1,
          lastAccessed: new Date().toISOString()
        }
      })),

      setCourseLesson: (lessonId) => set((state) => ({
        course: {
          ...state.course,
          currentLesson: lessonId,
          lastAccessed: new Date().toISOString()
        }
      })),

      completeLesson: (moduleId, lessonId) => set((state) => {
        const lessonKey = `${moduleId}-${lessonId}`
        const newCompletedLessons = [...state.course.completedLessons]
        if (!newCompletedLessons.includes(lessonKey)) {
          newCompletedLessons.push(lessonKey)
        }

        return {
          course: {
            ...state.course,
            completedLessons: newCompletedLessons,
            progress: calculateCourseProgress(newCompletedLessons)
          }
        }
      }),

      completeModule: (moduleId) => set((state) => {
        const newCompletedModules = [...state.course.completedModules]
        if (!newCompletedModules.includes(moduleId)) {
          newCompletedModules.push(moduleId)
        }

        return {
          course: {
            ...state.course,
            completedModules: newCompletedModules
          }
        }
      }),

      addAchievement: (achievement) => set((state) => ({
        course: {
          ...state.course,
          achievements: [...state.course.achievements, {
            ...achievement,
            id: Date.now().toString(),
            earnedAt: new Date().toISOString()
          }]
        }
      })),

      saveQuizScore: (moduleId, lessonId, score) => set((state) => ({
        course: {
          ...state.course,
          quizScores: {
            ...state.course.quizScores,
            [`${moduleId}-${lessonId}`]: score
          }
        }
      })),

      addBookmark: (moduleId, lessonId, content) => set((state) => ({
        course: {
          ...state.course,
          bookmarks: [...state.course.bookmarks, {
            id: Date.now().toString(),
            moduleId,
            lessonId,
            content,
            createdAt: new Date().toISOString()
          }]
        }
      })),

      removeBookmark: (bookmarkId) => set((state) => ({
        course: {
          ...state.course,
          bookmarks: state.course.bookmarks.filter(b => b.id !== bookmarkId)
        }
      })),

      saveCourseNote: (moduleId, lessonId, note) => set((state) => ({
        course: {
          ...state.course,
          notes: {
            ...state.course.notes,
            [`${moduleId}-${lessonId}`]: note
          }
        }
      })),

      // Custom checklist builder
      customChecklists: [],

      addCustomChecklist: (checklist) => set((state) => ({
        customChecklists: [...state.customChecklists, {
          ...checklist,
          id: Date.now().toString(),
          createdAt: new Date().toISOString()
        }]
      })),

      updateCustomChecklist: (id, updates) => set((state) => ({
        customChecklists: state.customChecklists.map(list =>
          list.id === id ? { ...list, ...updates, updatedAt: new Date().toISOString() } : list
        )
      })),

      deleteCustomChecklist: (id) => set((state) => ({
        customChecklists: state.customChecklists.filter(list => list.id !== id)
      })),

      // Notifications and reminders
      notifications: [],
      
      addNotification: (notification) => set((state) => ({
        notifications: [...state.notifications, {
          ...notification,
          id: Date.now().toString(),
          timestamp: new Date().toISOString(),
          read: false
        }]
      })),

      markNotificationRead: (id) => set((state) => ({
        notifications: state.notifications.map(notif =>
          notif.id === id ? { ...notif, read: true } : notif
        )
      })),

      clearNotifications: () => set({ notifications: [] }),

      // Cloud sync functions
      initializeDatabase: async () => {
        try {
          const success = await initializeCloudDatabase()
          if (success) {
            console.log('Database initialized successfully')
          }
          return success
        } catch (error) {
          console.error('Failed to initialize database:', error)
          return false
        }
      },

      syncCourseToCloud: async () => {
        try {
          const state = get()
          const success = await saveCourseProgress(state.course)
          return success
        } catch (error) {
          console.error('Failed to sync course to cloud:', error)
          return false
        }
      },

      loadCourseFromCloud: async () => {
        try {
          const courseData = await loadCourseProgress()
          if (courseData) {
            set((state) => ({
              course: { ...state.course, ...courseData }
            }))
            console.log('Course data loaded from cloud')
            return true
          }
          return false
        } catch (error) {
          console.error('Failed to load course from cloud:', error)
          return false
        }
      },
    }),
    {
      name: 'limitless-options-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({
        theme: state.theme,
        preferences: state.preferences,
        checklist: state.checklist,
        journal: state.journal,
        course: state.course,
        customChecklists: state.customChecklists,
        stats: state.stats,
      }),
    }
  )
)

// Helper function to calculate streak
function calculateStreak(dates) {
  if (!dates || dates.length === 0) return 0

  const sortedDates = dates.sort().reverse()
  let streak = 0
  const today = new Date()

  for (let i = 0; i < sortedDates.length; i++) {
    const date = new Date(sortedDates[i])
    const daysDiff = Math.floor((today - date) / (1000 * 60 * 60 * 24))

    if (daysDiff === i) {
      streak++
    } else {
      break
    }
  }

  return streak
}

// Helper function to calculate course progress
function calculateCourseProgress(completedLessons) {
  const totalLessons = 35 // Total lessons across all modules
  return Math.round((completedLessons.length / totalLessons) * 100)
}

// Selector hooks for better performance
export const useTheme = () => useStore((state) => state.theme)
export const useChecklist = () => useStore((state) => state.checklist)
export const useJournal = () => useStore((state) => state.journal)
export const useStats = () => useStore((state) => state.stats)
export const useUI = () => useStore((state) => state.ui)
export const usePreferences = () => useStore((state) => state.preferences)
export const useNotifications = () => useStore((state) => state.notifications)
export const useCourse = () => useStore((state) => state.course)
