/**
 * Database Migration Utility
 * Creates tables and migrates localStorage data to Neon PostgreSQL
 */

import { query, initializeDatabase } from './db.js'
import { 
  saveChecklistToCloud, 
  saveJournalToCloud, 
  saveCourseProgressToCloud,
  ensureUser 
} from './cloudStorage.js'

/**
 * Migrate localStorage data to cloud database
 */
export const migrateLocalStorageToCloud = async () => {
  console.log('🚀 Starting migration from localStorage to cloud...')
  
  try {
    // Step 1: Initialize database tables
    console.log('📋 Creating database tables...')
    await initializeDatabase()
    
    // Step 2: Ensure default user exists
    console.log('👤 Creating default user...')
    await ensureUser(1)
    
    // Step 3: Migrate checklist data
    console.log('✅ Migrating checklist data...')
    const checklistMigrated = await migrateChecklistData()
    
    // Step 4: Migrate journal data
    console.log('📝 Migrating journal data...')
    const journalMigrated = await migrateJournalData()
    
    // Step 5: Migrate course progress
    console.log('🎓 Migrating course progress...')
    const courseMigrated = await migrateCourseProgress()
    
    // Step 6: Migrate user preferences
    console.log('⚙️ Migrating user preferences...')
    const preferencesMigrated = await migrateUserPreferences()
    
    const summary = {
      success: true,
      checklistEntries: checklistMigrated,
      journalEntries: journalMigrated,
      courseProgress: courseMigrated,
      preferences: preferencesMigrated,
      timestamp: new Date().toISOString()
    }
    
    console.log('🎉 Migration completed successfully!')
    console.log('📊 Migration Summary:', summary)
    
    return summary
    
  } catch (error) {
    console.error('❌ Migration failed:', error)
    throw error
  }
}

/**
 * Migrate checklist data from localStorage
 */
const migrateChecklistData = async () => {
  let migratedCount = 0
  
  try {
    // Get all localStorage keys for checklists
    const checklistKeys = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('limitless_checklist_')) {
        checklistKeys.push(key)
      }
    }
    
    console.log(`📋 Found ${checklistKeys.length} checklist entries to migrate`)
    
    for (const key of checklistKeys) {
      try {
        const data = localStorage.getItem(key)
        if (data) {
          const parsedData = JSON.parse(data)
          const date = key.replace('limitless_checklist_', '')
          
          // Transform localStorage format to cloud format
          const cloudData = {
            items: parsedData.checkedItems || parsedData.items || {},
            notes: parsedData.notes || {},
            confidenceRating: parsedData.confidenceRating || 5,
            sessionStartTime: parsedData.sessionStartTime || null,
            isSessionActive: parsedData.isSessionActive || false
          }
          
          const success = await saveChecklistToCloud(date, cloudData)
          if (success) {
            migratedCount++
            console.log(`✅ Migrated checklist for ${date}`)
          } else {
            console.warn(`⚠️ Failed to migrate checklist for ${date}`)
          }
        }
      } catch (error) {
        console.error(`❌ Error migrating checklist ${key}:`, error)
      }
    }
    
    return migratedCount
  } catch (error) {
    console.error('❌ Error in checklist migration:', error)
    return migratedCount
  }
}

/**
 * Migrate journal data from localStorage
 */
const migrateJournalData = async () => {
  let migratedCount = 0
  
  try {
    // Get all localStorage keys for journals
    const journalKeys = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith('limitless_journal_')) {
        journalKeys.push(key)
      }
    }
    
    console.log(`📝 Found ${journalKeys.length} journal entries to migrate`)
    
    for (const key of journalKeys) {
      try {
        const data = localStorage.getItem(key)
        if (data) {
          const parsedData = JSON.parse(data)
          const date = key.replace('limitless_journal_', '')
          
          const success = await saveJournalToCloud(
            date, 
            parsedData.content || '', 
            parsedData.tags || []
          )
          
          if (success) {
            migratedCount++
            console.log(`✅ Migrated journal for ${date}`)
          } else {
            console.warn(`⚠️ Failed to migrate journal for ${date}`)
          }
        }
      } catch (error) {
        console.error(`❌ Error migrating journal ${key}:`, error)
      }
    }
    
    return migratedCount
  } catch (error) {
    console.error('❌ Error in journal migration:', error)
    return migratedCount
  }
}

/**
 * Migrate course progress from localStorage/Zustand store
 */
const migrateCourseProgress = async () => {
  try {
    // Get course data from Zustand store (localStorage)
    const storeData = localStorage.getItem('limitless-options-store')
    if (!storeData) {
      console.log('📚 No course progress found to migrate')
      return false
    }
    
    const parsedStore = JSON.parse(storeData)
    const courseData = parsedStore.state?.course
    
    if (!courseData) {
      console.log('📚 No course data in store to migrate')
      return false
    }
    
    const success = await saveCourseProgressToCloud(courseData)
    if (success) {
      console.log('✅ Migrated course progress')
      return true
    } else {
      console.warn('⚠️ Failed to migrate course progress')
      return false
    }
  } catch (error) {
    console.error('❌ Error migrating course progress:', error)
    return false
  }
}

/**
 * Migrate user preferences
 */
const migrateUserPreferences = async () => {
  try {
    // Get preferences from Zustand store
    const storeData = localStorage.getItem('limitless-options-store')
    if (!storeData) {
      console.log('⚙️ No preferences found to migrate')
      return false
    }
    
    const parsedStore = JSON.parse(storeData)
    const theme = parsedStore.state?.theme || 'light'
    const preferences = parsedStore.state?.preferences || {}
    
    // Save preferences to database
    await query(`
      INSERT INTO user_preferences (
        user_id, theme, language, notifications, auto_save, 
        confidence_rating, session_timer
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7)
      ON CONFLICT (user_id)
      DO UPDATE SET 
        theme = $2,
        language = $3,
        notifications = $4,
        auto_save = $5,
        confidence_rating = $6,
        session_timer = $7,
        updated_at = CURRENT_TIMESTAMP
    `, [
      1, // user ID
      theme,
      preferences.language || 'en',
      preferences.notifications !== false,
      preferences.autoSave !== false,
      preferences.confidenceRating !== false,
      preferences.sessionTimer || false
    ])
    
    console.log('✅ Migrated user preferences')
    return true
  } catch (error) {
    console.error('❌ Error migrating preferences:', error)
    return false
  }
}

/**
 * Check migration status
 */
export const checkMigrationStatus = async () => {
  try {
    // Check if tables exist and have data
    const checklistCount = await query('SELECT COUNT(*) FROM checklists WHERE user_id = 1')
    const journalCount = await query('SELECT COUNT(*) FROM journal_entries WHERE user_id = 1')
    const courseExists = await query('SELECT COUNT(*) FROM course_progress WHERE user_id = 1')
    const preferencesExists = await query('SELECT COUNT(*) FROM user_preferences WHERE user_id = 1')
    
    return {
      tablesExist: true,
      checklistEntries: parseInt(checklistCount.rows[0].count),
      journalEntries: parseInt(journalCount.rows[0].count),
      courseProgress: parseInt(courseExists.rows[0].count) > 0,
      preferences: parseInt(preferencesExists.rows[0].count) > 0
    }
  } catch (error) {
    console.error('Error checking migration status:', error)
    return {
      tablesExist: false,
      checklistEntries: 0,
      journalEntries: 0,
      courseProgress: false,
      preferences: false
    }
  }
}
