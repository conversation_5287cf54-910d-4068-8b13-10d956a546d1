/**
 * Database connection utility for Neon PostgreSQL
 * Simple, minimal setup for cloud storage
 */

import { Pool } from 'pg'

// Create a connection pool for better performance
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  },
  max: 20, // Maximum number of connections
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

/**
 * Execute a database query
 * @param {string} text - SQL query
 * @param {Array} params - Query parameters
 * @returns {Promise} Query result
 */
export const query = async (text, params) => {
  const start = Date.now()
  try {
    const res = await pool.query(text, params)
    const duration = Date.now() - start
    console.log('Executed query', { text, duration, rows: res.rowCount })
    return res
  } catch (error) {
    console.error('Database query error:', error)
    throw error
  }
}

/**
 * Get a client from the pool for transactions
 * @returns {Promise} Database client
 */
export const getClient = async () => {
  return await pool.connect()
}

/**
 * Initialize database tables
 * Creates tables if they don't exist
 */
export const initializeDatabase = async () => {
  try {
    // Users table for basic user management
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE,
        username VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Checklists table
    await query(`
      CREATE TABLE IF NOT EXISTS checklists (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        date DATE NOT NULL,
        items JSONB DEFAULT '{}',
        notes JSONB DEFAULT '{}',
        confidence_rating INTEGER DEFAULT 5,
        session_start_time TIMESTAMP,
        is_session_active BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, date)
      )
    `)

    // Journal entries table
    await query(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        date DATE NOT NULL,
        content TEXT,
        tags JSONB DEFAULT '[]',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id, date)
      )
    `)

    // Course progress table
    await query(`
      CREATE TABLE IF NOT EXISTS course_progress (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        current_module INTEGER DEFAULT 1,
        current_lesson INTEGER DEFAULT 1,
        completed_lessons JSONB DEFAULT '[]',
        completed_modules JSONB DEFAULT '[]',
        progress INTEGER DEFAULT 0,
        achievements JSONB DEFAULT '[]',
        quiz_scores JSONB DEFAULT '{}',
        bookmarks JSONB DEFAULT '[]',
        notes JSONB DEFAULT '{}',
        last_accessed TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id)
      )
    `)

    // User preferences table
    await query(`
      CREATE TABLE IF NOT EXISTS user_preferences (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        theme VARCHAR(20) DEFAULT 'light',
        language VARCHAR(10) DEFAULT 'en',
        notifications BOOLEAN DEFAULT TRUE,
        auto_save BOOLEAN DEFAULT TRUE,
        confidence_rating BOOLEAN DEFAULT TRUE,
        session_timer BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(user_id)
      )
    `)

    console.log('Database tables initialized successfully')
    return true
  } catch (error) {
    console.error('Error initializing database:', error)
    throw error
  }
}

export default pool
