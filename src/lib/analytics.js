/**
 * Real Analytics Service
 * Calculates actual user statistics from real data
 */

import { query } from './db.js'

/**
 * Get real user statistics from database
 */
export const getUserAnalytics = async (userId = 1, days = 30) => {
  try {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000))

    // Get checklist data
    const checklistResult = await query(`
      SELECT 
        date,
        items,
        confidence_rating,
        created_at
      FROM checklists 
      WHERE user_id = $1 
        AND date >= $2 
        AND date <= $3
      ORDER BY date DESC
    `, [userId, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]])

    // Get journal data
    const journalResult = await query(`
      SELECT 
        date,
        content,
        tags,
        created_at
      FROM journal_entries 
      WHERE user_id = $1 
        AND date >= $2 
        AND date <= $3
      ORDER BY date DESC
    `, [userId, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]])

    // Get course progress
    const courseResult = await query(`
      SELECT * FROM course_progress WHERE user_id = $1
    `, [userId])

    const checklists = checklistResult.rows
    const journals = journalResult.rows
    const courseProgress = courseResult.rows[0]

    return {
      checklists,
      journals,
      courseProgress,
      summary: calculateSummaryStats(checklists, journals, courseProgress)
    }
  } catch (error) {
    console.error('Error getting user analytics:', error)
    return {
      checklists: [],
      journals: [],
      courseProgress: null,
      summary: getEmptyStats()
    }
  }
}

/**
 * Calculate summary statistics from real data
 */
const calculateSummaryStats = (checklists, journals, courseProgress) => {
  const totalDays = 30

  // Checklist statistics
  const checklistDays = checklists.length
  const checklistCompletionRate = Math.round((checklistDays / totalDays) * 100)
  
  // Calculate average checklist score
  let totalChecklistScore = 0
  let checklistItemCount = 0
  
  checklists.forEach(checklist => {
    if (checklist.items) {
      const completedItems = Object.values(checklist.items).filter(Boolean).length
      totalChecklistScore += completedItems
      checklistItemCount++
    }
  })
  
  const avgChecklistScore = checklistItemCount > 0 ? (totalChecklistScore / checklistItemCount).toFixed(1) : '0.0'

  // Calculate average confidence
  const confidenceRatings = checklists
    .map(c => c.confidence_rating)
    .filter(rating => rating && rating > 0)
  
  const avgConfidence = confidenceRatings.length > 0 
    ? (confidenceRatings.reduce((sum, rating) => sum + rating, 0) / confidenceRatings.length).toFixed(1)
    : '0.0'

  // Journal statistics
  const journalDays = journals.length
  const journalCompletionRate = Math.round((journalDays / totalDays) * 100)
  
  // Calculate average journal length
  const journalLengths = journals
    .map(j => j.content ? j.content.length : 0)
    .filter(length => length > 0)
  
  const avgJournalLength = journalLengths.length > 0
    ? Math.round(journalLengths.reduce((sum, length) => sum + length, 0) / journalLengths.length)
    : 0

  // Combined statistics
  const bothCompletedDays = checklists.filter(checklist => 
    journals.some(journal => journal.date.toISOString().split('T')[0] === checklist.date.toISOString().split('T')[0])
  ).length
  
  const goalAchievementRate = Math.round((bothCompletedDays / totalDays) * 100)

  // Course statistics
  const courseStats = courseProgress ? {
    currentModule: courseProgress.current_module,
    currentLesson: courseProgress.current_lesson,
    completedLessons: courseProgress.completed_lessons?.length || 0,
    completedModules: courseProgress.completed_modules?.length || 0,
    progress: courseProgress.progress || 0,
    achievements: courseProgress.achievements?.length || 0
  } : {
    currentModule: 1,
    currentLesson: 1,
    completedLessons: 0,
    completedModules: 0,
    progress: 0,
    achievements: 0
  }

  // Calculate streak
  const streak = calculateStreak(checklists.map(c => c.date.toISOString().split('T')[0]))

  return {
    checklist: {
      totalDays: checklistDays,
      completionRate: checklistCompletionRate,
      avgScore: avgChecklistScore,
      avgConfidence: avgConfidence
    },
    journal: {
      totalDays: journalDays,
      completionRate: journalCompletionRate,
      avgLength: avgJournalLength
    },
    combined: {
      bothCompletedDays,
      goalAchievementRate,
      streak
    },
    course: courseStats
  }
}

/**
 * Calculate actual streak from dates
 */
const calculateStreak = (dates) => {
  if (!dates || dates.length === 0) return 0
  
  const sortedDates = dates.sort().reverse()
  let streak = 0
  const today = new Date().toISOString().split('T')[0]
  
  // Check if today or yesterday has activity
  const latestDate = sortedDates[0]
  const daysDiff = Math.floor((new Date(today) - new Date(latestDate)) / (1000 * 60 * 60 * 24))
  
  if (daysDiff > 1) return 0 // Streak broken
  
  // Count consecutive days
  for (let i = 0; i < sortedDates.length; i++) {
    const currentDate = new Date(sortedDates[i])
    const expectedDate = new Date(today)
    expectedDate.setDate(expectedDate.getDate() - i)
    
    if (currentDate.toISOString().split('T')[0] === expectedDate.toISOString().split('T')[0]) {
      streak++
    } else {
      break
    }
  }
  
  return streak
}

/**
 * Get empty stats structure
 */
const getEmptyStats = () => ({
  checklist: {
    totalDays: 0,
    completionRate: 0,
    avgScore: '0.0',
    avgConfidence: '0.0'
  },
  journal: {
    totalDays: 0,
    completionRate: 0,
    avgLength: 0
  },
  combined: {
    bothCompletedDays: 0,
    goalAchievementRate: 0,
    streak: 0
  },
  course: {
    currentModule: 1,
    currentLesson: 1,
    completedLessons: 0,
    completedModules: 0,
    progress: 0,
    achievements: 0
  }
})

/**
 * Get performance trend data
 */
export const getPerformanceTrend = async (userId = 1, days = 30) => {
  try {
    const endDate = new Date()
    const startDate = new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000))

    const result = await query(`
      SELECT 
        c.date,
        c.items,
        c.confidence_rating,
        CASE WHEN j.content IS NOT NULL AND LENGTH(j.content) > 0 THEN 1 ELSE 0 END as has_journal
      FROM checklists c
      LEFT JOIN journal_entries j ON c.date = j.date AND c.user_id = j.user_id
      WHERE c.user_id = $1 
        AND c.date >= $2 
        AND c.date <= $3
      ORDER BY c.date ASC
    `, [userId, startDate.toISOString().split('T')[0], endDate.toISOString().split('T')[0]])

    return result.rows.map(row => ({
      date: new Date(row.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      checklist: row.items ? Object.values(row.items).filter(Boolean).length : 0,
      journal: row.has_journal,
      confidence: row.confidence_rating || 0
    }))
  } catch (error) {
    console.error('Error getting performance trend:', error)
    return []
  }
}
