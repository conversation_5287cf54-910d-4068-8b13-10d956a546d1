/**
 * Cloud Storage Service for Neon PostgreSQL
 * Handles all database operations with simple, clean API
 */

import { query } from './db.js'

// Simple user ID for now (in production, use proper authentication)
const DEFAULT_USER_ID = 1

/**
 * Ensure user exists in database
 */
export const ensureUser = async (userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT id FROM users WHERE id = $1',
      [userId]
    )
    
    if (result.rows.length === 0) {
      await query(
        'INSERT INTO users (id, username) VALUES ($1, $2) ON CONFLICT (id) DO NOTHING',
        [userId, 'default_user']
      )
    }
    return userId
  } catch (error) {
    console.error('Error ensuring user:', error)
    throw error
  }
}

/**
 * Save checklist data to cloud
 */
export const saveChecklistToCloud = async (date, data, userId = DEFAULT_USER_ID) => {
  try {
    await ensureUser(userId)
    
    const result = await query(`
      INSERT INTO checklists (user_id, date, items, notes, confidence_rating, session_start_time, is_session_active, updated_at)
      VALUES ($1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP)
      ON CONFLICT (user_id, date) 
      DO UPDATE SET 
        items = $3,
        notes = $4,
        confidence_rating = $5,
        session_start_time = $6,
        is_session_active = $7,
        updated_at = CURRENT_TIMESTAMP
    `, [
      userId,
      date,
      JSON.stringify(data.items || {}),
      JSON.stringify(data.notes || {}),
      data.confidenceRating || 5,
      data.sessionStartTime || null,
      data.isSessionActive || false
    ])
    
    return true
  } catch (error) {
    console.error('Error saving checklist to cloud:', error)
    return false
  }
}

/**
 * Load checklist data from cloud
 */
export const loadChecklistFromCloud = async (date, userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT * FROM checklists WHERE user_id = $1 AND date = $2',
      [userId, date]
    )
    
    if (result.rows.length === 0) {
      return null
    }
    
    const row = result.rows[0]
    return {
      items: row.items || {},
      notes: row.notes || {},
      confidenceRating: row.confidence_rating || 5,
      sessionStartTime: row.session_start_time,
      isSessionActive: row.is_session_active || false,
      lastUpdated: row.updated_at
    }
  } catch (error) {
    console.error('Error loading checklist from cloud:', error)
    return null
  }
}

/**
 * Save journal entry to cloud
 */
export const saveJournalToCloud = async (date, content, tags = [], userId = DEFAULT_USER_ID) => {
  try {
    await ensureUser(userId)
    
    await query(`
      INSERT INTO journal_entries (user_id, date, content, tags, updated_at)
      VALUES ($1, $2, $3, $4, CURRENT_TIMESTAMP)
      ON CONFLICT (user_id, date)
      DO UPDATE SET 
        content = $3,
        tags = $4,
        updated_at = CURRENT_TIMESTAMP
    `, [
      userId,
      date,
      content,
      JSON.stringify(tags)
    ])
    
    return true
  } catch (error) {
    console.error('Error saving journal to cloud:', error)
    return false
  }
}

/**
 * Load journal entry from cloud
 */
export const loadJournalFromCloud = async (date, userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT * FROM journal_entries WHERE user_id = $1 AND date = $2',
      [userId, date]
    )
    
    if (result.rows.length === 0) {
      return null
    }
    
    const row = result.rows[0]
    return {
      content: row.content || '',
      tags: row.tags || [],
      lastUpdated: row.updated_at
    }
  } catch (error) {
    console.error('Error loading journal from cloud:', error)
    return null
  }
}

/**
 * Save course progress to cloud
 */
export const saveCourseProgressToCloud = async (courseData, userId = DEFAULT_USER_ID) => {
  try {
    await ensureUser(userId)
    
    await query(`
      INSERT INTO course_progress (
        user_id, current_module, current_lesson, completed_lessons, 
        completed_modules, progress, achievements, quiz_scores, 
        bookmarks, notes, last_accessed, updated_at
      )
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP)
      ON CONFLICT (user_id)
      DO UPDATE SET 
        current_module = $2,
        current_lesson = $3,
        completed_lessons = $4,
        completed_modules = $5,
        progress = $6,
        achievements = $7,
        quiz_scores = $8,
        bookmarks = $9,
        notes = $10,
        last_accessed = $11,
        updated_at = CURRENT_TIMESTAMP
    `, [
      userId,
      courseData.currentModule,
      courseData.currentLesson,
      JSON.stringify(courseData.completedLessons),
      JSON.stringify(courseData.completedModules),
      courseData.progress,
      JSON.stringify(courseData.achievements),
      JSON.stringify(courseData.quizScores),
      JSON.stringify(courseData.bookmarks),
      JSON.stringify(courseData.notes),
      courseData.lastAccessed
    ])
    
    return true
  } catch (error) {
    console.error('Error saving course progress to cloud:', error)
    return false
  }
}

/**
 * Load course progress from cloud
 */
export const loadCourseProgressFromCloud = async (userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT * FROM course_progress WHERE user_id = $1',
      [userId]
    )
    
    if (result.rows.length === 0) {
      return null
    }
    
    const row = result.rows[0]
    return {
      currentModule: row.current_module,
      currentLesson: row.current_lesson,
      completedLessons: row.completed_lessons || [],
      completedModules: row.completed_modules || [],
      progress: row.progress || 0,
      achievements: row.achievements || [],
      quizScores: row.quiz_scores || {},
      bookmarks: row.bookmarks || [],
      notes: row.notes || {},
      lastAccessed: row.last_accessed
    }
  } catch (error) {
    console.error('Error loading course progress from cloud:', error)
    return null
  }
}

/**
 * Get all checklist dates from cloud
 */
export const getAllChecklistDatesFromCloud = async (userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT date FROM checklists WHERE user_id = $1 ORDER BY date DESC',
      [userId]
    )
    
    return result.rows.map(row => row.date.toISOString().split('T')[0])
  } catch (error) {
    console.error('Error getting checklist dates from cloud:', error)
    return []
  }
}

/**
 * Get all journal dates from cloud
 */
export const getAllJournalDatesFromCloud = async (userId = DEFAULT_USER_ID) => {
  try {
    const result = await query(
      'SELECT date FROM journal_entries WHERE user_id = $1 ORDER BY date DESC',
      [userId]
    )
    
    return result.rows.map(row => row.date.toISOString().split('T')[0])
  } catch (error) {
    console.error('Error getting journal dates from cloud:', error)
    return []
  }
}
