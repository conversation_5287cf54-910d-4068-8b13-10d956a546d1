/**
 * Data Validation Service
 * Ensures all data is real and accurate, no mock/sample data
 */

/**
 * Validate checklist data
 */
export const validateChecklistData = (data) => {
  if (!data || typeof data !== 'object') {
    return { isValid: false, error: 'Invalid checklist data format' }
  }

  // Validate items
  if (data.items && typeof data.items !== 'object') {
    return { isValid: false, error: 'Checklist items must be an object' }
  }

  // Validate notes
  if (data.notes && typeof data.notes !== 'object') {
    return { isValid: false, error: 'Checklist notes must be an object' }
  }

  // Validate confidence rating
  if (data.confidenceRating && (typeof data.confidenceRating !== 'number' || data.confidenceRating < 1 || data.confidenceRating > 5)) {
    return { isValid: false, error: 'Confidence rating must be a number between 1 and 5' }
  }

  return { isValid: true }
}

/**
 * Validate journal data
 */
export const validateJournalData = (data) => {
  if (!data || typeof data !== 'object') {
    return { isValid: false, error: 'Invalid journal data format' }
  }

  // Validate content
  if (data.content && typeof data.content !== 'string') {
    return { isValid: false, error: 'Journal content must be a string' }
  }

  // Validate tags
  if (data.tags && !Array.isArray(data.tags)) {
    return { isValid: false, error: 'Journal tags must be an array' }
  }

  return { isValid: true }
}

/**
 * Validate course progress data
 */
export const validateCourseData = (data) => {
  if (!data || typeof data !== 'object') {
    return { isValid: false, error: 'Invalid course data format' }
  }

  // Validate module and lesson numbers
  if (data.currentModule && (typeof data.currentModule !== 'number' || data.currentModule < 1)) {
    return { isValid: false, error: 'Current module must be a positive number' }
  }

  if (data.currentLesson && (typeof data.currentLesson !== 'number' || data.currentLesson < 1)) {
    return { isValid: false, error: 'Current lesson must be a positive number' }
  }

  // Validate arrays
  if (data.completedLessons && !Array.isArray(data.completedLessons)) {
    return { isValid: false, error: 'Completed lessons must be an array' }
  }

  if (data.completedModules && !Array.isArray(data.completedModules)) {
    return { isValid: false, error: 'Completed modules must be an array' }
  }

  if (data.achievements && !Array.isArray(data.achievements)) {
    return { isValid: false, error: 'Achievements must be an array' }
  }

  if (data.bookmarks && !Array.isArray(data.bookmarks)) {
    return { isValid: false, error: 'Bookmarks must be an array' }
  }

  // Validate progress percentage
  if (data.progress && (typeof data.progress !== 'number' || data.progress < 0 || data.progress > 100)) {
    return { isValid: false, error: 'Progress must be a number between 0 and 100' }
  }

  return { isValid: true }
}

/**
 * Validate date format
 */
export const validateDate = (dateString) => {
  if (!dateString || typeof dateString !== 'string') {
    return { isValid: false, error: 'Date must be a string' }
  }

  const date = new Date(dateString)
  if (isNaN(date.getTime())) {
    return { isValid: false, error: 'Invalid date format' }
  }

  // Check if date is not in the future (for data integrity)
  const today = new Date()
  today.setHours(23, 59, 59, 999) // End of today
  
  if (date > today) {
    return { isValid: false, error: 'Date cannot be in the future' }
  }

  return { isValid: true }
}

/**
 * Sanitize user input to prevent XSS and other attacks
 */
export const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input

  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
}

/**
 * Validate analytics data to ensure it's real
 */
export const validateAnalyticsData = (data) => {
  if (!data || typeof data !== 'object') {
    return { isValid: false, error: 'Invalid analytics data format' }
  }

  // Check for obviously fake data patterns
  if (data.performance && Array.isArray(data.performance)) {
    const hasRepeatingPatterns = data.performance.some((item, index) => {
      if (index === 0) return false
      const prev = data.performance[index - 1]
      return item.checklist === prev.checklist && 
             item.journal === prev.journal && 
             item.confidence === prev.confidence
    })

    if (hasRepeatingPatterns) {
      console.warn('Potential mock data detected in analytics')
    }
  }

  return { isValid: true }
}

/**
 * Check if data appears to be sample/mock data
 */
export const detectMockData = (data, type) => {
  const mockPatterns = {
    checklist: [
      'sample', 'demo', 'test', 'mock', 'fake', 'placeholder'
    ],
    journal: [
      'lorem ipsum', 'sample entry', 'test journal', 'demo content'
    ],
    course: [
      'sample progress', 'demo achievement', 'test completion'
    ]
  }

  const patterns = mockPatterns[type] || []
  
  if (typeof data === 'string') {
    const lowerData = data.toLowerCase()
    return patterns.some(pattern => lowerData.includes(pattern))
  }

  if (typeof data === 'object' && data !== null) {
    const jsonString = JSON.stringify(data).toLowerCase()
    return patterns.some(pattern => jsonString.includes(pattern))
  }

  return false
}

/**
 * Ensure data integrity across the application
 */
export const ensureDataIntegrity = (data, type) => {
  // Validate data format
  let validation
  switch (type) {
    case 'checklist':
      validation = validateChecklistData(data)
      break
    case 'journal':
      validation = validateJournalData(data)
      break
    case 'course':
      validation = validateCourseData(data)
      break
    default:
      validation = { isValid: true }
  }

  if (!validation.isValid) {
    throw new Error(`Data validation failed: ${validation.error}`)
  }

  // Check for mock data
  if (detectMockData(data, type)) {
    console.warn(`Potential mock data detected in ${type} data`)
  }

  return data
}

/**
 * Clean up any remaining sample data
 */
export const cleanSampleData = (data) => {
  if (typeof data === 'string') {
    return data
      .replace(/sample|demo|test|mock|fake|placeholder/gi, '')
      .replace(/lorem ipsum/gi, '')
      .trim()
  }

  if (Array.isArray(data)) {
    return data.filter(item => !detectMockData(item, 'general'))
  }

  if (typeof data === 'object' && data !== null) {
    const cleaned = {}
    for (const [key, value] of Object.entries(data)) {
      if (!detectMockData(value, 'general')) {
        cleaned[key] = cleanSampleData(value)
      }
    }
    return cleaned
  }

  return data
}
